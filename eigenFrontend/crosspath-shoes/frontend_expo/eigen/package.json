{"name": "eigen", "main": "expo-router/entry", "expo": {"jsEngine": "hermes", "entryPoint": "./polyfills.js"}, "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "postinstall": "node ./scripts/patch-svg.js && patch-package", "apply-patches": "node ./scripts/patch-svg.js && patch-package"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "2.49.5-next.1", "date-fns": "^2.30.0", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.10.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17", "zod": "^3.25.20"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.8.3"}, "private": true}