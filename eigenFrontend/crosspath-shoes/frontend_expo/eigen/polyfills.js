/**
 * This file contains minimal polyfills needed by React Native and Expo
 * to support Supabase authentication.
 */

// Import the URL polyfill (needed for Supabase)
import 'react-native-url-polyfill/auto';

// Import crypto polyfill (needed for Supabase)
import 'react-native-get-random-values';

// Disable WebSocket to prevent Node.js dependencies
global.WebSocket = undefined;

// Disable other Node.js modules that might be imported
global.Buffer = undefined;
global.process = undefined;

// Make sure the entry point is registered
import 'expo-router/entry';
