import React, { forwardRef } from "react";
import { TextInput, View, Text } from "react-native";

interface InputProps extends React.ComponentProps<typeof TextInput> {
  label?: string;
  error?: string;
  className?: string;
  containerClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export const Input = forwardRef<TextInput, InputProps>(
  (
    {
      label,
      error,
      className = "",
      containerClassName = "",
      labelClassName = "",
      errorClassName = "",
      ...props
    },
    ref
  ) => {
    return (
      <View className={`w-full ${containerClassName}`}>
        {label && (
          <Text
            className={`text-sm font-medium text-foreground mb-1.5 ${labelClassName}`}
          >
            {label}
          </Text>
        )}
        <TextInput
          ref={ref}
          className={`bg-background border border-input rounded-md px-3 py-2 text-foreground ${
            error ? "border-destructive" : ""
          } ${className}`}
          placeholderTextColor="#9CA3AF"
          {...props}
        />
        {error && (
          <Text className={`text-xs text-destructive mt-1 ${errorClassName}`}>
            {error}
          </Text>
        )}
      </View>
    );
  }
);
