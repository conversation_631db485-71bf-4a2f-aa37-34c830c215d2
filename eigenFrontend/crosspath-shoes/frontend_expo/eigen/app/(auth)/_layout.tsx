import React from 'react';
import { View, Text, SafeAreaView, Image } from 'react-native';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';


export default function AuthLayout() {
  return (
    <SafeAreaView className="flex-1 bg-background">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="p-4 border-b border-border/20">
        <View className="flex-row items-center justify-center">
          <Text className="text-xl font-bold text-foreground">eigen</Text>
        </View>
      </View>
      
      {/* Background Pattern */}
      <View className="absolute inset-0 w-full h-full opacity-10 pointer-events-none z-0" />
      
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: 'transparent' },
        }}
      />
      
      {/* Footer */}
      <View className="p-6 border-t border-border/20 mt-auto">
        <Text className="text-xs text-center text-muted-foreground">
          © {new Date().getFullYear()} CrossPath Labs // eigen Division
        </Text>
        <View className="flex-row justify-center mt-2 space-x-3">
          <Text className="text-xs text-muted-foreground">Privacy</Text>
          <Text className="text-xs text-muted-foreground">|</Text>
          <Text className="text-xs text-muted-foreground">Terms</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}
