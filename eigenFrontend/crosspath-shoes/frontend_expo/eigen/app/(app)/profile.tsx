import React from "react";
import { View, Text, ScrollView, Al<PERSON> } from "react-native";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../../components/ui/Button";
import { Card, CardContent } from "../../components/ui/Card";
import { router } from "expo-router";
import { LogOut, User, Settings, ChevronRight } from "lucide-react-native";

export default function ProfileScreen() {
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    Alert.alert(
      "Sign Out",
      "Are you sure you want to sign out?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Sign Out",
          style: "destructive",
          onPress: signOut,
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <ScrollView className="flex-1 bg-background">
      <View className="p-4">
        <View className="items-center py-6">
          <View className="w-20 h-20 rounded-full bg-primary/10 items-center justify-center mb-4">
            <User size={40} color="#23453b" />
          </View>
          <Text className="text-xl font-semibold text-foreground">
            {user?.user_metadata?.name || user?.email?.split("@")[0] || "User"}
          </Text>
          <Text className="text-sm text-muted-foreground mt-1">
            {user?.email}
          </Text>
        </View>

        <View className="space-y-4 mt-4">
          <Card className="border-border/50">
            <CardContent className="p-0">
              <View className="py-2">
                <View className="flex-row items-center justify-between px-4 py-3 border-b border-border">
                  <View className="flex-row items-center">
                    <Settings size={20} color="#23453b" />
                    <Text className="ml-3 text-foreground">
                      Account Settings
                    </Text>
                  </View>
                  <ChevronRight size={20} color="#6b7280" />
                </View>

                <View className="flex-row items-center justify-between px-4 py-3">
                  <View className="flex-row items-center">
                    <LogOut size={20} color="#ef4444" />
                    <Text className="ml-3 text-destructive">Sign Out</Text>
                  </View>
                  <Button
                    onPress={handleSignOut}
                    variant="ghost"
                    className="p-0 m-0"
                  >
                    <ChevronRight size={20} color="#6b7280" />
                  </Button>
                </View>
              </View>
            </CardContent>
          </Card>

          <View className="mt-8">
            <Text className="text-xs text-center text-muted-foreground">
              App Version 1.0.0
            </Text>
            <Text className="text-xs text-center text-muted-foreground mt-1">
              © {new Date().getFullYear()} CrossPath Labs // eigen Division
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
