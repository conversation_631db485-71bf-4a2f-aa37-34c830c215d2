import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import {
  ArrowLeft,
  CalendarDays,
  Star,
  ChevronDown,
  ChevronUp,
  UserCircle,
  Footprints,
  Video,
  Weight,
  Ruler,
  Target,
  Mountain,
  Clock,
  Activity,
  ShieldAlert,
  Thermometer,
} from "lucide-react-native";
import { supabase } from "../../../utils/supabase";
import { Button } from "../../../components/ui/Button";
import { Card } from "../../../components/ui/Card";
import { format } from "date-fns";

// Define the RunningProfile type
type RunningProfile = {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  age?: number;
  gender?: string;
  weightKg?: number;
  heightCm?: number;
  runningGoal?: string;
  terrain?: string;
  heelToToeLengthLeft?: number;
  heelToToeLengthRight?: number;
  forefootWidthLeft?: number;
  forefootWidthRight?: number;
  medialArchLengthLeft?: number;
  medialArchLengthRight?: number;
  medialArchHeightLeft?: number;
  medialArchHeightRight?: number;
  footImageTopLeftUrl?: string;
  footImageTopRightUrl?: string;
  footImageMedialLeftUrl?: string;
  footImageMedialRightUrl?: string;
  averageWeeklyKm?: number;
  averagePaceEasyLong?: string;
  averageCadence?: number;
  previousInjuries?: string;
  climate?: string;
  runningVideoSagittalUrl?: string;
  overstride?: number;
  trunkLean?: number;
  verticalOscillationVideo?: number;
  kneeFlexionLoading?: number;
  runningPower?: number;
  groundContactTime?: number;
  verticalOscillationWearable?: number;
  recommendation?: {
    shoeModel1?: { id: string; name: string; fullName: string; brand: string };
    shoeModel2?: { id: string; name: string; fullName: string; brand: string };
    shoeModel3?: { id: string; name: string; fullName: string; brand: string };
    shoeModel4?: { id: string; name: string; fullName: string; brand: string };
    shoeModel5?: { id: string; name: string; fullName: string; brand: string };
    explanation1?: string;
    explanation2?: string;
    explanation3?: string;
    explanation4?: string;
    explanation5?: string;
  };
};

// Data item component for displaying profile data
interface DataItemProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ComponentType<{ size: number; color: string }>;
  unit?: string;
}

const DataItem = ({ label, value, icon: Icon, unit = "" }: DataItemProps) => (
  <View className="flex-row items-start space-x-3 p-3 bg-secondary/30 rounded-lg">
    {Icon && <Icon size={16} color="#23453b" />}
    <View className="flex-1">
      <Text className="text-xs font-medium text-muted-foreground">{label}</Text>
      <Text className="text-sm font-semibold text-foreground">
        {value !== null && value !== undefined ? `${value}${unit}` : "N/A"}
      </Text>
    </View>
  </View>
);

// Profile Detail Card component
interface ProfileDetailCardProps {
  title: string;
  icon: React.ReactNode;
  isExpanded: boolean;
  children: React.ReactNode;
}

const ProfileDetailCard = ({
  title,
  icon,
  isExpanded,
  children,
}: ProfileDetailCardProps) => (
  <Card className="border-border/50 overflow-hidden mb-4">
    <View className="p-4">
      <View className="flex-row items-center mb-3">
        {icon}
        <Text className="text-lg font-medium text-foreground ml-2">
          {title}
        </Text>
      </View>
      {children}
    </View>
  </Card>
);

export default function ProfileDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [profile, setProfile] = useState<RunningProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  useEffect(() => {
    if (!id) return;
    fetchProfile(id);
  }, [id]);

  const fetchProfile = async (profileId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("RunningProfile")
        .select(
          `
          *,
          recommendation (
            id,
            shoeModel1Id,
            shoeModel2Id,
            shoeModel3Id,
            shoeModel4Id,
            shoeModel5Id,
            explanation1,
            explanation2,
            explanation3,
            explanation4,
            explanation5,
            shoeModel1 (id, name, fullName, brand),
            shoeModel2 (id, name, fullName, brand),
            shoeModel3 (id, name, fullName, brand),
            shoeModel4 (id, name, fullName, brand),
            shoeModel5 (id, name, fullName, brand)
          )
        `
        )
        .eq("id", profileId)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (err) {
      console.error("Error fetching profile:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load running profile"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <ActivityIndicator size="large" color="#23453b" />
      </View>
    );
  }

  if (error || !profile) {
    return (
      <View className="flex-1 p-4 bg-background">
        <Card className="bg-red-100 border border-red-500/50">
          <View className="p-3">
            <Text className="text-red-800 text-xs">
              {error || "Profile not found"}
            </Text>
          </View>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-background">
      <View className="p-4">
        {/* Back button */}
        <TouchableOpacity
          className="flex-row items-center mb-3"
          onPress={() => router.back()}
        >
          <ArrowLeft size={16} color="#6b7280" />
          <Text className="ml-1 text-sm text-muted-foreground">
            Back to Profiles
          </Text>
        </TouchableOpacity>

        {/* Profile Header */}
        <Card className="border-border/50 overflow-hidden mb-4">
          <View className="bg-brand-green h-32">
            <View className="absolute bottom-0 left-0 p-4">
              <View className="flex-row items-center gap-2">
                <Text className="text-2xl font-semibold text-white">
                  {profile.name}
                </Text>
                {profile.isDefault && (
                  <View className="bg-white/20 px-2 py-0.5 rounded-full flex-row items-center">
                    <Star size={12} color="#fff" />
                    <Text className="ml-1 text-xs text-white">Default</Text>
                  </View>
                )}
              </View>
              <View className="flex-row items-center mt-1">
                <CalendarDays size={12} color="#fff" opacity={0.8} />
                <Text className="ml-1 text-xs text-white/80">
                  Created: {format(new Date(profile.createdAt), "dd MMM, yyyy")}
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Toggle Expand/Collapse Button */}
        <View className="items-center mb-4">
          <Button
            onPress={() => setIsExpanded(!isExpanded)}
            variant="outline"
            className="bg-secondary text-secondary-foreground w-full max-w-xs py-2"
          >
            <View className="flex-row items-center">
              {isExpanded ? (
                <>
                  <ChevronUp size={16} color="#374151" />
                  <Text className="ml-2 text-foreground">
                    Collapse Profile Details
                  </Text>
                </>
              ) : (
                <>
                  <ChevronDown size={16} color="#374151" />
                  <Text className="ml-2 text-foreground">
                    Expand Profile Details
                  </Text>
                </>
              )}
            </View>
          </Button>
        </View>

        {/* Profile Details Cards */}
        <ProfileDetailCard
          title="Runner Information"
          icon={<UserCircle size={20} color="#23453b" />}
          isExpanded={isExpanded}
        >
          {isExpanded ? (
            <View className="grid grid-cols-1 gap-3">
              <DataItem
                label="Age"
                value={profile.age}
                unit=" years"
                icon={CalendarDays}
              />
              <DataItem
                label="Sex/Gender"
                value={profile.gender}
                icon={UserCircle}
              />
              <DataItem
                label="Weight"
                value={profile.weightKg}
                unit=" kg"
                icon={Weight}
              />
              <DataItem
                label="Height"
                value={profile.heightCm}
                unit=" cm"
                icon={Ruler}
              />
              <DataItem
                label="Running Goal"
                value={profile.runningGoal}
                icon={Target}
              />
              <DataItem
                label="Primary Terrain"
                value={profile.terrain}
                icon={Mountain}
              />
            </View>
          ) : (
            <View className="items-center justify-center">
              <Text className="text-sm text-muted-foreground">
                Click "Expand Profile Details" to view runner information
              </Text>
            </View>
          )}
        </ProfileDetailCard>

        <ProfileDetailCard
          title="Foot Measurements"
          icon={<Footprints size={20} color="#23453b" />}
          isExpanded={isExpanded}
        >
          {isExpanded ? (
            <View className="space-y-4">
              <View className="grid grid-cols-1 gap-3">
                <DataItem
                  label="Foot Type"
                  value={"Normal"}
                  icon={Footprints}
                />
                <DataItem
                  label="Pronation"
                  value={"Neutral"}
                  icon={Footprints}
                />
              </View>

              <Text className="text-xs font-medium text-muted-foreground mb-2">
                Foot Dimensions
              </Text>
              <View className="grid grid-cols-1 gap-3">
                <DataItem
                  label="Left Foot Length"
                  value={profile.heelToToeLengthLeft}
                  unit=" mm"
                  icon={Ruler}
                />
                <DataItem
                  label="Right Foot Length"
                  value={profile.heelToToeLengthRight}
                  unit=" mm"
                  icon={Ruler}
                />
                <DataItem
                  label="Left Forefoot Width"
                  value={profile.forefootWidthLeft}
                  unit=" mm"
                  icon={Ruler}
                />
                <DataItem
                  label="Right Forefoot Width"
                  value={profile.forefootWidthRight}
                  unit=" mm"
                  icon={Ruler}
                />
              </View>
            </View>
          ) : (
            <View className="items-center justify-center">
              <Text className="text-sm text-muted-foreground">
                Click "Expand Profile Details" to view foot measurements
              </Text>
            </View>
          )}
        </ProfileDetailCard>

        <ProfileDetailCard
          title="Running Habits"
          icon={<Activity size={20} color="#23453b" />}
          isExpanded={isExpanded}
        >
          {isExpanded ? (
            <View className="grid grid-cols-1 gap-3">
              <DataItem
                label="Weekly Mileage"
                value={profile.averageWeeklyKm}
                unit=" km"
                icon={Activity}
              />
              <DataItem
                label="Avg. Pace (Easy/Long)"
                value={profile.averagePaceEasyLong}
                icon={Clock}
              />
              <DataItem
                label="Avg. Running Cadence"
                value={profile.averageCadence}
                unit=" spm"
                icon={Activity}
              />
              <DataItem
                label="Previous Injuries"
                value={profile.previousInjuries}
                icon={ShieldAlert}
              />
              <DataItem
                label="Typical Climate"
                value={profile.climate}
                icon={Thermometer}
              />
            </View>
          ) : (
            <View className="items-center justify-center">
              <Text className="text-sm text-muted-foreground">
                Click "Expand Profile Details" to view running habits
              </Text>
            </View>
          )}
        </ProfileDetailCard>

        {profile.runningVideoSagittalUrl && (
          <ProfileDetailCard
            title="Video Analysis"
            icon={<Video size={20} color="#23453b" />}
            isExpanded={isExpanded}
          >
            {isExpanded ? (
              <View className="space-y-4">
                <View className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                  {/* Video would be here - using a placeholder for now */}
                  <View className="flex-1 items-center justify-center bg-black/10">
                    <Video size={40} color="#23453b" opacity={0.5} />
                  </View>
                </View>

                <Text className="text-xs font-medium text-muted-foreground mb-2">
                  Running Form Analysis
                </Text>
                <View className="grid grid-cols-1 gap-3">
                  <DataItem
                    label="Overstride"
                    value={
                      profile.overstride !== null &&
                      profile.overstride !== undefined
                        ? profile.overstride > 0.5
                          ? "Significant"
                          : profile.overstride > 0.2
                          ? "Moderate"
                          : "Minimal"
                        : "N/A"
                    }
                    icon={Activity}
                  />
                  <DataItem
                    label="Trunk Lean"
                    value={
                      profile.trunkLean !== null &&
                      profile.trunkLean !== undefined
                        ? `${Math.round(profile.trunkLean)}°`
                        : "N/A"
                    }
                    icon={Activity}
                  />
                  <DataItem
                    label="Vertical Oscillation"
                    value={
                      profile.verticalOscillationVideo !== null &&
                      profile.verticalOscillationVideo !== undefined
                        ? `${profile.verticalOscillationVideo} cm`
                        : "N/A"
                    }
                    icon={Activity}
                  />
                  <DataItem
                    label="Knee Flexion"
                    value={
                      profile.kneeFlexionLoading !== null &&
                      profile.kneeFlexionLoading !== undefined
                        ? `${Math.round(profile.kneeFlexionLoading)}°`
                        : "N/A"
                    }
                    icon={Activity}
                  />
                </View>
              </View>
            ) : (
              <View className="items-center justify-center">
                <Text className="text-sm text-muted-foreground">
                  Click "Expand Profile Details" to view video analysis
                </Text>
              </View>
            )}
          </ProfileDetailCard>
        )}

        {/* Recommendations Section */}
        <Card className="border-border/50 overflow-hidden mb-4">
          <View className="p-6">
            <Text className="text-lg font-medium mb-4">
              Shoe Recommendations
            </Text>

            {profile.recommendation ? (
              <View className="space-y-4">
                {/* Top Recommendations */}
                {profile.recommendation.shoeModel1 && (
                  <TouchableOpacity
                    className="border border-primary/20 bg-primary/5 rounded-lg p-4"
                    onPress={() =>
                      router.push(
                        `/shoes/${profile.recommendation?.shoeModel1?.id}`
                      )
                    }
                  >
                    <View className="bg-primary px-2 py-0.5 rounded-full self-start mb-1">
                      <Text className="text-xs text-white">Top Pick</Text>
                    </View>
                    <Text className="text-base font-medium">
                      {profile.recommendation.shoeModel1.fullName}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      {profile.recommendation.shoeModel1.brand}
                    </Text>
                    <Text className="text-xs text-muted-foreground mt-2">
                      {profile.recommendation.explanation1
                        ? profile.recommendation.explanation1.length > 100
                          ? profile.recommendation.explanation1.substring(
                              0,
                              100
                            ) + "..."
                          : profile.recommendation.explanation1
                        : "No explanation available"}
                    </Text>
                  </TouchableOpacity>
                )}

                {profile.recommendation.shoeModel2 && (
                  <TouchableOpacity
                    className="border border-border/50 rounded-lg p-4"
                    onPress={() =>
                      router.push(
                        `/shoes/${profile.recommendation?.shoeModel2?.id}`
                      )
                    }
                  >
                    <View className="bg-secondary px-2 py-0.5 rounded-full self-start mb-1">
                      <Text className="text-xs text-secondary-foreground">
                        #2
                      </Text>
                    </View>
                    <Text className="text-base font-medium">
                      {profile.recommendation.shoeModel2.fullName}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      {profile.recommendation.shoeModel2.brand}
                    </Text>
                    <Text className="text-xs text-muted-foreground mt-2">
                      {profile.recommendation.explanation2
                        ? profile.recommendation.explanation2.length > 100
                          ? profile.recommendation.explanation2.substring(
                              0,
                              100
                            ) + "..."
                          : profile.recommendation.explanation2
                        : "No explanation available"}
                    </Text>
                  </TouchableOpacity>
                )}

                {profile.recommendation.shoeModel3 && (
                  <TouchableOpacity
                    className="border border-border/50 rounded-lg p-4"
                    onPress={() =>
                      router.push(
                        `/shoes/${profile.recommendation?.shoeModel3?.id}`
                      )
                    }
                  >
                    <View className="bg-secondary px-2 py-0.5 rounded-full self-start mb-1">
                      <Text className="text-xs text-secondary-foreground">
                        #3
                      </Text>
                    </View>
                    <Text className="text-base font-medium">
                      {profile.recommendation.shoeModel3.fullName}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      {profile.recommendation.shoeModel3.brand}
                    </Text>
                    <Text className="text-xs text-muted-foreground mt-2">
                      {profile.recommendation.explanation3
                        ? profile.recommendation.explanation3.length > 100
                          ? profile.recommendation.explanation3.substring(
                              0,
                              100
                            ) + "..."
                          : profile.recommendation.explanation3
                        : "No explanation available"}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View className="items-center justify-center py-8">
                <Text className="text-lg font-medium text-foreground mb-2">
                  No Recommendations Yet
                </Text>
                <Text className="text-sm text-muted-foreground text-center max-w-md mb-4">
                  This profile doesn't have any shoe recommendations yet.
                </Text>
                <Button
                  onPress={() =>
                    router.push(`/running-profiles/${profile.id}/analyze`)
                  }
                  className="bg-primary"
                >
                  <Text className="text-white">Generate Recommendations</Text>
                </Button>
              </View>
            )}
          </View>
        </Card>

        {/* Profile Metadata */}
        <View className="mt-4 mb-8 items-center">
          <Text className="text-xs text-muted-foreground text-center">
            Profile ID: {profile.id} • Last updated:{" "}
            {format(new Date(profile.updatedAt), "MMMM d, yyyy")}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}
