import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { router, useNavigation } from "expo-router";
import {
  UserCircle,
  PlusCircle,
  Star,
  Calendar,
  ChevronLeft,
  ChevronRight,
  PlayCircle,
} from "lucide-react-native";
import { supabase } from "../../../utils/supabase";
import { Button } from "../../../components/ui/Button";
import { Card, CardContent } from "../../../components/ui/Card";
import { format } from "date-fns";

// Define the RunningProfile type
type RunningProfile = {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  runningGoal?: string;
  runningVideoSagittalUrl?: string;
  recommendation?: {
    shoeModel1?: { id: string; name: string };
    shoeModel2?: { id: string; name: string };
    shoeModel3?: { id: string; name: string };
    shoeModel4?: { id: string; name: string };
    shoeModel5?: { id: string; name: string };
    explanation1?: string;
  };
};

export default function RunningProfilesScreen() {
  const [profiles, setProfiles] = useState<RunningProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get("window").width;

  useEffect(() => {
    fetchProfiles();
  }, []);

  const fetchProfiles = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch profiles from Supabase
      const { data, error } = await supabase
        .from("RunningProfile")
        .select(
          `
          id,
          name,
          createdAt,
          updatedAt,
          isDefault,
          runningGoal,
          runningVideoSagittalUrl,
          recommendation (
            id,
            shoeModel1Id,
            shoeModel2Id,
            shoeModel3Id,
            shoeModel4Id,
            shoeModel5Id,
            explanation1,
            shoeModel1 (id, name),
            shoeModel2 (id, name),
            shoeModel3 (id, name),
            shoeModel4 (id, name),
            shoeModel5 (id, name)
          )
        `
        )
        .order("isDefault", { ascending: false })
        .order("createdAt", { ascending: false });

      if (error) throw error;

      setProfiles(data || []);
    } catch (err) {
      console.error("Error fetching profiles:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleProfilePress = (profile: RunningProfile) => {
    router.push(`/running-profiles/${profile.id}`);
  };

  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      const newSlide = currentSlide - 1;
      setCurrentSlide(newSlide);
      flatListRef.current?.scrollToIndex({ index: newSlide, animated: true });
    }
  };

  const handleNextSlide = () => {
    if (currentSlide < profiles.length) {
      const newSlide = currentSlide + 1;
      setCurrentSlide(newSlide);
      flatListRef.current?.scrollToIndex({ index: newSlide, animated: true });
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd.MM.yyyy");
  };

  const renderProfileCard = ({
    item,
    index,
  }: {
    item: RunningProfile | null;
    index: number;
  }) => {
    // If item is null, render the "Create New Profile" card
    if (item === null) {
      return (
        <Card
          className="h-[500px] bg-brand-green rounded-2xl overflow-hidden"
          onPress={() => router.push("/running-profile/new")}
        >
          <View className="flex-1 p-5">
            <Text className="text-xl font-semibold text-white mb-1">
              Create New Profile
            </Text>
            <Text className="text-sm text-white/80">
              Add another runner or variation
            </Text>

            <View className="flex-1 items-center justify-center">
              <View className="w-20 h-20 rounded-full bg-white/20 items-center justify-center">
                <PlusCircle size={40} color="#fff" />
              </View>
            </View>
          </View>
        </Card>
      );
    }

    // Regular profile card
    const profile = item;
    const isDefault = profile.isDefault;
    const showVideo =
      isDefault || (index === 0 && !profiles.some((p) => p.isDefault));

    // Get recommendations (up to 5)
    const recommendations = [];
    if (profile.recommendation?.shoeModel1) {
      recommendations.push({
        name: profile.recommendation.shoeModel1.name,
        id: profile.recommendation.shoeModel1.id,
      });
    }
    if (profile.recommendation?.shoeModel2) {
      recommendations.push({
        name: profile.recommendation.shoeModel2.name,
        id: profile.recommendation.shoeModel2.id,
      });
    }
    if (profile.recommendation?.shoeModel3) {
      recommendations.push({
        name: profile.recommendation.shoeModel3.name,
        id: profile.recommendation.shoeModel3.id,
      });
    }
    if (profile.recommendation?.shoeModel4) {
      recommendations.push({
        name: profile.recommendation.shoeModel4.name,
        id: profile.recommendation.shoeModel4.id,
      });
    }
    if (profile.recommendation?.shoeModel5) {
      recommendations.push({
        name: profile.recommendation.shoeModel5.name,
        id: profile.recommendation.shoeModel5.id,
      });
    }

    return (
      <Card
        className="h-[500px] bg-black/20 rounded-2xl overflow-hidden"
        onPress={() => handleProfilePress(profile)}
      >
        {/* Background Media */}
        <View className="absolute inset-0 w-full h-full">
          {showVideo && profile.runningVideoSagittalUrl ? (
            <View className="flex-1 bg-black">
              {/* Video would be here - using a placeholder for now */}
              <View className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
            </View>
          ) : (
            <View className="flex-1 bg-gradient-to-br from-primary/20 to-primary/40">
              <View className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
            </View>
          )}
        </View>

        {/* Content Overlay */}
        <View className="flex-1 z-10">
          {/* Header */}
          <View className="p-5 flex-row justify-between items-start">
            <View>
              <Text className="text-xl font-semibold text-white mb-1">
                {profile.name}
              </Text>
              <View className="flex-row items-center">
                <Calendar size={12} color="#fff" opacity={0.8} />
                <Text className="ml-1 text-xs text-white/80">
                  {formatDate(profile.createdAt)}
                </Text>
                {isDefault && (
                  <View className="ml-2 flex-row items-center bg-white/20 px-2 py-0.5 rounded-full">
                    <Star size={12} color="#fff" />
                    <Text className="ml-1 text-xs text-white">Default</Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {/* Main Content Area */}
          <View className="flex-1 p-5 pt-0 pb-2">
            {/* Profile Info */}
            {profile.runningGoal && (
              <View className="mb-3">
                <Text className="text-sm text-white/90">
                  <Text className="font-medium">Goal:</Text>{" "}
                  {profile.runningGoal}
                </Text>
              </View>
            )}

            {/* Video Play Button (centered) */}
            {showVideo && profile.runningVideoSagittalUrl && (
              <View className="flex-1 items-center justify-center">
                <View className="w-16 h-16 rounded-full bg-white/20 items-center justify-center">
                  <PlayCircle size={32} color="#fff" />
                </View>
              </View>
            )}

            {/* If no video, add spacer */}
            {(!showVideo || !profile.runningVideoSagittalUrl) && (
              <View className="flex-1" />
            )}

            {/* Recommendations Section */}
            {recommendations.length > 0 && (
              <View className="mt-auto mb-0">
                <Text className="text-sm font-medium text-white/90 mb-2">
                  Top Recommendations
                </Text>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  className="pb-1"
                >
                  <View className="flex-row gap-2">
                    {recommendations.map((rec, idx) => (
                      <TouchableOpacity
                        key={rec.id}
                        className="px-4 py-3 rounded-lg bg-black/40 border border-white/10"
                        onPress={() => router.push(`/shoes/${rec.id}`)}
                      >
                        <View className="flex-row items-center">
                          <View className="w-6 h-6 rounded-full bg-white/10 items-center justify-center mr-2">
                            <Text className="text-xs text-white/80">
                              {idx + 1}
                            </Text>
                          </View>
                          <Text className="text-xs text-white/90">
                            {rec.name}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}
          </View>
        </View>
      </Card>
    );
  };

  if (loading) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <ActivityIndicator size="large" color="#23453b" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 p-4 bg-background">
        <Card className="bg-red-100 border border-red-500/50">
          <CardContent className="p-3">
            <Text className="text-red-800 text-xs">{error}</Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  if (profiles.length === 0) {
    return (
      <View className="flex-1 p-4 items-center justify-center bg-background">
        <View className="border-2 border-dashed border-zinc-300 rounded-lg p-8 items-center w-full max-w-md">
          <UserCircle size={40} color="#a1a1aa" />
          <Text className="text-lg font-medium text-zinc-800 mt-4 mb-2">
            No running profiles yet
          </Text>
          <Text className="text-sm text-zinc-500 mb-4 text-center">
            Create your first profile to unlock personalized shoe matches.
          </Text>
          <Button
            onPress={() => router.push("/running-profile/new")}
            className="bg-brand-green"
          >
            <View className="flex-row items-center">
              <PlusCircle size={16} color="#fff" />
              <Text className="ml-1.5 text-white">Create Profile</Text>
            </View>
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 p-4 bg-background">
      {/* Page Title */}
      <View className="flex-row items-center justify-between mb-6">
        <Text className="text-2xl font-semibold text-foreground">
          Running Profiles
        </Text>
        <Button
          onPress={() => router.push("/running-profile/new")}
          className="bg-primary"
        >
          <View className="flex-row items-center">
            <PlusCircle size={16} color="#fff" />
            <Text className="ml-2 text-white">New Profile</Text>
          </View>
        </Button>
      </View>

      {/* Profiles List */}
      <FlatList
        ref={flatListRef}
        data={[...profiles, null]} // Add null for the "Create New Profile" card
        renderItem={renderProfileCard}
        keyExtractor={(item, index) => (item ? item.id : "new-profile")}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        snapToInterval={screenWidth - 32} // Account for padding
        decelerationRate="fast"
        contentContainerStyle={{ paddingHorizontal: 0 }}
        onMomentumScrollEnd={(e) => {
          const newIndex = Math.round(
            e.nativeEvent.contentOffset.x / (screenWidth - 32)
          );
          setCurrentSlide(newIndex);
        }}
        ItemSeparatorComponent={() => <View className="w-4" />}
      />

      {/* Navigation Arrows */}
      {profiles.length > 0 && (
        <View className="flex-row justify-center items-center mt-4 mb-6 gap-8">
          <TouchableOpacity
            onPress={handlePrevSlide}
            disabled={currentSlide === 0}
            className={`h-10 w-10 rounded-full bg-white shadow items-center justify-center ${
              currentSlide === 0 ? "opacity-50" : ""
            }`}
          >
            <ChevronLeft size={20} color="#374151" />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleNextSlide}
            disabled={currentSlide === profiles.length}
            className={`h-10 w-10 rounded-full bg-white shadow items-center justify-center ${
              currentSlide === profiles.length ? "opacity-50" : ""
            }`}
          >
            <ChevronRight size={20} color="#374151" />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
