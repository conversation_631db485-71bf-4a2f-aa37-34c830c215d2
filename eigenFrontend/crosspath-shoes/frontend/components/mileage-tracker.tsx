"use client"

interface MileageTrackerProps {
  currentMileage: number
  maxMileage: number
}

export function MileageTracker({ currentMileage, maxMileage }: MileageTrackerProps) {
  const percentage = Math.min((currentMileage / maxMileage) * 100, 100)

  // Determine color based on percentage
  let color = "bg-primary"
  if (percentage > 80) {
    color = "bg-destructive"
  } else if (percentage > 60) {
    color = "bg-yellow-500"
  }

  return (
    <div className="w-full mt-2">
      <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
        <div
          className={`h-full ${color} transition-all duration-500 ease-in-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}

