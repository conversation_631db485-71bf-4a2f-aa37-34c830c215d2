import React, { useState, useEffect } from "react";
import Image from "next/image";
import { ImageIcon, Video } from "lucide-react";
import {
  getSignedUrl,
  isValidMediaUrl,
  isFakeMediaData,
} from "@/lib/media-handler";

interface MediaDisplayProps {
  url: string | null | undefined;
  type: "image" | "video";
  label?: string;
  className?: string;
  aspectRatio?: "square" | "video" | string;
  showFakeDataLabel?: boolean;
}

export function MediaDisplay({
  url,
  type,
  label,
  className = "",
  aspectRatio = "square",
  showFakeDataLabel = true,
}: MediaDisplayProps) {
  const [signedUrl, setSignedUrl] = useState<string>(url || "");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Determine aspect ratio class
  const aspectRatioClass =
    aspectRatio === "square"
      ? "aspect-square"
      : aspectRatio === "video"
      ? "aspect-video"
      : aspectRatio;

  // Get signed URL when component mounts or URL changes
  useEffect(() => {
    const fetchSignedUrl = async () => {
      if (!url) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // If URL already contains a token parameter, it's already signed
        if (url.includes("supabase.co") && !url.includes("token=")) {
          const result = await getSignedUrl(url);
          setSignedUrl(result);
        } else {
          // URL is either already signed or not a Supabase URL
          setSignedUrl(url);
        }
      } catch (err) {
        console.error(`Error getting signed URL for ${url}:`, err);
        setError(
          err instanceof Error ? err.message : "Failed to get signed URL"
        );
        setSignedUrl(url || "");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSignedUrl();
  }, [url]);

  // Check if URL is valid and if it's fake data
  const isValidUrl = isValidMediaUrl(url);
  const isFakeData = isFakeMediaData(url);

  // Render placeholder if no URL or loading
  if (!url || (!isValidUrl && !isLoading)) {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center ${className}`}
        >
          {type === "image" ? (
            <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          ) : (
            <Video className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label} {!url ? "(Missing)" : ""}
          </span>
        )}
      </div>
    );
  }

  // Render image
  if (type === "image") {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className}`}
        >
          <div className="w-full h-full relative">
            <Image
              src={signedUrl}
              alt={label || "Image"}
              fill
              className="object-cover"
              unoptimized={true} // Skip Next.js image optimization for all URLs
              onError={() => {
                console.error(`Failed to load image: ${signedUrl}`);
                setError("Failed to load image");
              }}
            />
            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800">
                <div className="text-center p-2">
                  <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600 mx-auto mb-2" />
                  <span className="text-xs text-zinc-500 dark:text-zinc-400">
                    Image could not be loaded
                  </span>
                </div>
              </div>
            )}
          </div>
          {showFakeDataLabel && isFakeData && (
            <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
              AI Generated
            </div>
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label}
          </span>
        )}
      </div>
    );
  }

  // Render video
  return (
    <div className="flex flex-col gap-2">
      <div
        className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className}`}
      >
        <video
          src={signedUrl}
          controls
          className="w-full h-full object-cover"
          poster={isFakeData ? "/images/video-placeholder.jpg" : undefined}
          onError={(e) => {
            console.error(`Failed to load video: ${signedUrl}`);
            setError("Failed to load video");

            // Show a message in the video container
            const target = e.currentTarget;
            const parent = target.parentElement;
            if (parent) {
              // Create error display element
              const errorDiv = document.createElement("div");
              errorDiv.className =
                "absolute inset-0 flex flex-col items-center justify-center bg-zinc-100 dark:bg-zinc-800 p-4 text-center";
              errorDiv.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-zinc-400 dark:text-zinc-600 mb-2"><polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect></svg>
                <span class="text-xs text-zinc-500 dark:text-zinc-400">Video could not be loaded</span>
              `;

              // Hide the video and show the error
              target.style.display = "none";
              parent.appendChild(errorDiv);
            }
          }}
        />
        {showFakeDataLabel && isFakeData && (
          <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
            AI Generated
          </div>
        )}
      </div>
      {label && (
        <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
          {label}
        </span>
      )}
    </div>
  );
}
