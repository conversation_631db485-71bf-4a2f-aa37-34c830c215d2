import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { ImageIcon, Video, Loader2 } from "lucide-react";
import { useMediaUrl } from "@/hooks/useStorageMedia";
import { StoragePath } from "@/lib/media-storage";

interface StorageMediaProps {
  storagePath: StoragePath | string | null | undefined;
  type: "image" | "video";
  label?: string;
  className?: string;
  aspectRatio?: "square" | "video" | string;
  showFakeDataLabel?: boolean;
  fallbackText?: string;
  expiresIn?: number;
  loopPreview?: boolean; // Add new prop for looping video preview
}

export function StorageMedia({
  storagePath,
  type,
  label,
  className = "",
  aspectRatio = "square",
  showFakeDataLabel = true,
  fallbackText = "No media available",
  expiresIn = 3600,
  loopPreview = false,
}: StorageMediaProps) {
  const { url, isLoading, error } = useMediaUrl(storagePath, expiresIn);
  const [mediaError, setMediaError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Effect to handle the looping of the first second of video
  useEffect(() => {
    if (loopPreview && videoRef.current && url) {
      const video = videoRef.current;

      // Function to reset video to beginning when it reaches 1 second
      const handleTimeUpdate = () => {
        if (video.currentTime >= 1.0) {
          video.currentTime = 0;
        }
      };

      video.addEventListener("timeupdate", handleTimeUpdate);

      return () => {
        video.removeEventListener("timeupdate", handleTimeUpdate);
      };
    }
  }, [loopPreview, url]);

  // Determine aspect ratio class
  const aspectRatioClass =
    aspectRatio === "square"
      ? "aspect-square"
      : aspectRatio === "video"
      ? "aspect-video"
      : aspectRatio;

  // Determine minimum height based on aspect ratio
  const minHeight =
    aspectRatio === "square"
      ? "150px"
      : aspectRatio === "video"
      ? "200px"
      : "150px";

  // Check if this is fake data
  const isFakeData =
    storagePath &&
    typeof storagePath === "string" &&
    (storagePath.includes("fake-foot-image") ||
      storagePath.includes("fake-running-video"));

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center ${className}`}
          style={{ minHeight: minHeight }}
        >
          <Loader2 className="h-8 w-8 text-zinc-400 dark:text-zinc-600 animate-spin" />
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label}
          </span>
        )}
      </div>
    );
  }

  // Render error or empty state
  if (!url || error || mediaError) {
    // Determine if this is an authentication error
    const isAuthError =
      error?.includes("Authentication") || error?.includes("Unauthorized");

    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center ${className}`}
          style={{ minHeight: minHeight }}
        >
          {type === "image" ? (
            <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          ) : (
            <Video className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          )}
          {(error || mediaError) && (
            <div
              className={`absolute bottom-0 left-0 right-0 ${
                isAuthError ? "bg-amber-500/70" : "bg-red-500/70"
              } text-white text-xs py-1 px-2 text-center`}
            >
              {isAuthError ? "Authentication required" : mediaError || error}
            </div>
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label} {!storagePath ? "(Missing)" : ""}
            {isAuthError && " (Login required)"}
          </span>
        )}
      </div>
    );
  }

  // Render image
  if (type === "image") {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className}`}
          style={{ minHeight: minHeight }}
        >
          <div
            className="w-full h-full relative"
            style={{ minHeight: minHeight }}
          >
            <Image
              src={url}
              alt={label || "Image"}
              fill
              className="object-cover"
              unoptimized={true} // Skip Next.js image optimization for all URLs
              onError={() => {
                console.error(`Failed to load image: ${url}`);
                setMediaError("Failed to load image");
              }}
            />
          </div>
          {showFakeDataLabel && isFakeData && (
            <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
              AI Generated
            </div>
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label}
          </span>
        )}
      </div>
    );
  }

  // Render video
  // Check if it's a .mov file
  const isMovFile = url && url.toLowerCase().endsWith(".mov");

  return (
    <div className="flex flex-col gap-2 w-full max-w-full overflow-hidden">
      <div
        className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className} w-full max-w-full`}
        style={{ minHeight: minHeight }}
      >
        {isMovFile ? (
          // For .mov files, use a different approach
          <div className="relative w-full h-full">
            {/* Use a poster image if available, otherwise a gradient background */}
            <div className="absolute inset-0 bg-gradient-to-r from-zinc-200 to-zinc-300 dark:from-zinc-800 dark:to-zinc-700"></div>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <Video className="h-16 w-16 text-white/80 drop-shadow-lg mb-2" />
              <p className="text-sm text-white/90 text-center px-4">
                This video format may not be supported in your browser.
                <br />
                <a
                  href={url}
                  download
                  className="underline hover:text-white mt-2 inline-block"
                >
                  Download video
                </a>
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full max-w-full overflow-hidden">
            <video
              ref={videoRef}
              src={url}
              controls={!loopPreview}
              autoPlay={loopPreview}
              loop={loopPreview}
              muted={loopPreview}
              playsInline={loopPreview}
              className="w-full h-full object-contain max-w-full"
              poster={isFakeData ? "/images/video-placeholder.jpg" : undefined}
              onError={() => {
                console.error(`Failed to load video: ${url}`);
                setMediaError("Failed to load video");
              }}
            />
          </div>
        )}
        {showFakeDataLabel && isFakeData && (
          <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
            AI Generated
          </div>
        )}
      </div>
      {label && (
        <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center truncate w-full">
          {label}
        </span>
      )}
    </div>
  );
}
