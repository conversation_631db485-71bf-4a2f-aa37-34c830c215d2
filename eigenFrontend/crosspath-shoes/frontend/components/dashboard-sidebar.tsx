"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Settings,
  BarChart3,
  Footprints,
  HelpCircle,
  UserRound,
} from "lucide-react";

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  matchExact?: boolean;
}

const sidebarLinks: NavItem[] = [
  {
    title: "Running Profiles",
    href: "/running-profiles",
    icon: LayoutDashboard,
    matchExact: true,
  },
  {
    title: "My Shoes",
    href: "/shoes",
    icon: Footprints,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
];

export function DashboardSidebar() {
  const pathname = usePathname();

  return (
    <aside className="hidden md:flex w-72 flex-col border-r border-brand-black/15 dark:border-text-main/15 bg-white dark:bg-sweetspot-black/40 p-4">
      <nav className="flex flex-col gap-1.5">
        {sidebarLinks.map((link) => {
          const isActive = link.matchExact
            ? pathname === link.href
            : pathname?.startsWith(link.href);

          return (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
                isActive
                  ? "bg-brand-green/10 dark:bg-brand-green/20 text-brand-green dark:text-emerald-300 font-semibold"
                  : "text-brand-black/70 dark:text-text-main/60 hover:bg-zinc-100/70 dark:hover:bg-terminal-black/60 hover:text-brand-black dark:hover:text-text-main/90"
              )}
            >
              <link.icon className="h-4 w-4" />
              {link.title}
            </Link>
          );
        })}
      </nav>
      <div className="mt-auto">
        <Link
          href="/support"
          className={cn(
            "flex items-center gap-3 rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
            pathname === "/support"
              ? "bg-brand-green/10 dark:bg-brand-green/20 text-brand-green dark:text-emerald-300 font-semibold"
              : "text-brand-black/70 dark:text-text-main/60 hover:bg-zinc-100/70 dark:hover:bg-terminal-black/60 hover:text-brand-black dark:hover:text-text-main/90"
          )}
        >
          <HelpCircle className="h-4 w-4" />
          Help & Support
        </Link>
      </div>
    </aside>
  );
}
