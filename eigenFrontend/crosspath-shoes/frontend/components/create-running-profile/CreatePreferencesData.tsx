import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Sliders } from "lucide-react";
import { RunningProfile } from "@/lib/types";

interface CreatePreferencesDataProps {
  profile: RunningProfile;
}

export function CreatePreferencesData({ profile }: CreatePreferencesDataProps) {
  const renderPreferenceItem = (
    label: string,
    value: string | null | undefined
  ) => {
    if (!value) return null;

    return (
      <div className="py-2">
        <div className="flex justify-between">
          <span className="text-sm text-zinc-500 dark:text-zinc-400">
            {label}
          </span>
          <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
            {value}
          </span>
        </div>
      </div>
    );
  };

  return <div className="space-y-4"></div>;
}
