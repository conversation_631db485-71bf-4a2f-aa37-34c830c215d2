import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Activity, Gauge } from "lucide-react";
import { RunningProfile } from "@prisma/client";

interface CreateAthleticDataProps {
  profile: RunningProfile;
}

export function CreateAthleticData({ profile }: CreateAthleticDataProps) {
  const renderDetailItem = (label: string, value: string | number | null | undefined) => {
    if (value === null || value === undefined || value === "") {
      return null;
    }
    
    return (
      <div className="py-2">
        <div className="flex justify-between">
          <span className="text-sm text-zinc-500 dark:text-zinc-400">{label}</span>
          <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">{value}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
          <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
            <Activity className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
            Running Habits
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <div className="grid sm:grid-cols-2 gap-x-6 divide-y sm:divide-y-0 divide-zinc-100 dark:divide-zinc-700/50">
            <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 sm:pr-3 sm:border-r border-zinc-100 dark:border-zinc-700/50">
              {renderDetailItem("Weekly Distance", profile.averageWeeklyKm)}
              {renderDetailItem("Average Pace", profile.averagePaceEasyLong)}
              {renderDetailItem("Average Cadence", profile.averageCadence ? `${profile.averageCadence} steps/min` : null)}
            </div>
            <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 pt-3 sm:pt-0 sm:pl-3">
              {renderDetailItem("Terrain", profile.terrain)}
              {renderDetailItem("Previous Injuries", profile.previousInjuries)}
              {renderDetailItem("Injury Severity", profile.previousInjuriesSeverity)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Wearable Data */}
      {(profile.runningPower || profile.groundContactTime || profile.verticalOscillationWearable) && (
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
            <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
              <Gauge className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
              Wearable Data
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-3">
            <div className="grid sm:grid-cols-2 gap-x-6 divide-y sm:divide-y-0 divide-zinc-100 dark:divide-zinc-700/50">
              <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 sm:pr-3 sm:border-r border-zinc-100 dark:border-zinc-700/50">
                {renderDetailItem("Running Power", profile.runningPower ? `${profile.runningPower} watts` : null)}
                {renderDetailItem("Ground Contact Time", profile.groundContactTime ? `${profile.groundContactTime} ms` : null)}
              </div>
              <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 pt-3 sm:pt-0 sm:pl-3">
                {renderDetailItem("Vertical Oscillation", profile.verticalOscillationWearable ? `${profile.verticalOscillationWearable} cm` : null)}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
