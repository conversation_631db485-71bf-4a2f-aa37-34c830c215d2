"use client";

import React, { useState, useEffect } from "react";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON>eft,
  ArrowRight,
  Loader2,
  Brain,
  CheckCircle,
  AlertCircle,
  Play,
} from "lucide-react";
import { toast } from "sonner";
import { motion } from "framer-motion";

interface VideoAnalysisStepProps {
  nextStepHref: string;
  backStepHref: string;
  currentStepIndex: number;
}

export const VideoAnalysisStep: React.FC<VideoAnalysisStepProps> = ({
  nextStepHref,
  backStepHref,
  currentStepIndex,
}) => {
  const { profileId, profileData, setCurrentStep } = useRunningProfile();
  const router = useRouter();

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [analysisStarted, setAnalysisStarted] = useState(false);

  // Check if sagittal video is available
  const sagittalVideoUrl = profileData.runningVideoSagittalUrl;
  const hasVideo = Boolean(sagittalVideoUrl);

  useEffect(() => {
    // Check if analysis has already been completed by looking for analysis data
    const hasAnalysisData = Boolean(
      profileData.pelvicDrop ||
      profileData.kneeDrift ||
      profileData.footDrift ||
      profileData.overstride ||
      profileData.ankleDorsiflexion
    );
    
    if (hasAnalysisData) {
      setAnalysisComplete(true);
      setAnalysisStarted(true);
    }
  }, [profileData]);

  const startAnalysis = async () => {
    if (!profileId || !sagittalVideoUrl) {
      toast.error("Missing profile or video data");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisError(null);
    setAnalysisStarted(true);

    try {
      const response = await fetch("/api/analyze-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          profileId,
          videoUrl: sagittalVideoUrl,
          videoType: "sagittal",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Analysis failed");
      }

      if (data.success) {
        setAnalysisComplete(true);
        toast.success("Video analysis completed successfully!");
      } else {
        throw new Error(data.error || "Analysis failed");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Analysis failed";
      setAnalysisError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleNext = () => {
    setCurrentStep(currentStepIndex + 1);
    router.push(nextStepHref);
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(backStepHref);
  };

  const getAnalysisStatusContent = () => {
    if (!hasVideo) {
      return {
        icon: <AlertCircle className="h-12 w-12 text-amber-500" />,
        title: "No Video Available",
        description: "Please upload a sagittal (side view) running video first.",
        action: null,
      };
    }

    if (analysisComplete) {
      return {
        icon: <CheckCircle className="h-12 w-12 text-green-500" />,
        title: "Analysis Complete",
        description: "Your running gait has been analyzed using Eigen Lab for Biomechanical Analysis using AI. The results have been added to your profile.",
        action: null,
      };
    }

    if (isAnalyzing) {
      return {
        icon: <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />,
        title: "Analyzing Your Gait",
        description: "Eigen Lab for Biomechanical Analysis using AI is processing your running video. This typically takes 10-30 seconds.",
        action: null,
      };
    }

    if (analysisError) {
      return {
        icon: <AlertCircle className="h-12 w-12 text-red-500" />,
        title: "Analysis Failed",
        description: analysisError,
        action: (
          <Button onClick={startAnalysis} className="mt-4">
            <Brain className="h-4 w-4 mr-2" />
            Retry Analysis
          </Button>
        ),
      };
    }

    if (!analysisStarted) {
      return {
        icon: <Brain className="h-12 w-12 text-blue-500" />,
        title: "Ready for Analysis",
        description: "Your sagittal running video is ready for biomechanical analysis. This will analyze your gait pattern and add the results to your profile.",
        action: (
          <Button onClick={startAnalysis} className="mt-4">
            <Play className="h-4 w-4 mr-2" />
            Start Analysis
          </Button>
        ),
      };
    }

    return null;
  };

  const statusContent = getAnalysisStatusContent();

  return (
    <div className="flex flex-col max-w-4xl mx-auto px-4 py-2">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col"
      >
        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Video Analysis</h2>
          <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
            We'll analyze your running video to extract biomechanical insights for your profile.
          </p>
        </div>

        {/* Analysis Status Card */}
        <div className="border border-border rounded-lg p-8 bg-muted/20 text-center mb-6">
          {statusContent && (
            <>
              <div className="flex justify-center mb-4">
                {statusContent.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2">{statusContent.title}</h3>
              <p className="text-sm text-muted-foreground mb-4 max-w-md mx-auto">
                {statusContent.description}
              </p>
              {statusContent.action}
            </>
          )}
        </div>

        {/* Video Preview (if available) */}
        {hasVideo && (
          <div className="border border-border rounded-lg p-4 bg-muted/10 mb-6">
            <h4 className="text-sm font-medium mb-2">Sagittal Running Video</h4>
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              <video
                src={sagittalVideoUrl}
                controls
                className="w-full h-full object-contain"
              >
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        )}
      </motion.div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-center pt-2 pb-4 gap-2">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={isAnalyzing}
          className="flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <Button
          onClick={handleNext}
          disabled={!analysisComplete && hasVideo}
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          {analysisComplete || !hasVideo ? (
            <>
              Next
              <ArrowRight className="h-4 w-4" />
            </>
          ) : (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing...
            </>
          )}
        </Button>
      </div>
    </div>
  );
};
