"use client";

import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import UserNav from "@/components/user-nav";

import { LogIn, UserPlus, LayoutDashboard } from "lucide-react";

export function LandingHeader() {
  const { user } = useAuth();
  const isAuthenticated = !!user;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center px-4 sm:px-6 max-w-7xl mx-auto">
        <div className="mr-4 sm:mr-8">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/logo.png"
              alt="CrossPath Labs - eigen"
              width={100}
              height={100}
              className="h-8 sm:h-10 w-auto"
            />
            <div className="flex flex-col"></div>
          </Link>
        </div>
        <nav className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="flex-1"></div>
          <div className="flex items-center gap-2">
            {isAuthenticated ? (
              <div className="flex items-center gap-2 sm:gap-4">
                {/* Running Profiles button - text on desktop, icon on mobile */}
                <Button asChild className="hidden sm:flex">
                  <Link href="/running-profiles">Running Profiles</Link>
                </Button>
                <Button asChild size="icon" className="sm:hidden">
                  <Link href="/running-profiles" aria-label="Running Profiles">
                    <LayoutDashboard className="h-4 w-4" />
                  </Link>
                </Button>
                <UserNav />
              </div>
            ) : (
              <>
                {/* Sign In button - text on desktop, icon on mobile */}
                <Button
                  variant="ghost"
                  className="hidden sm:flex text-sm"
                  asChild
                >
                  <Link href="/signin">Sign In</Link>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="sm:hidden"
                  asChild
                >
                  <Link href="/signin" aria-label="Sign In">
                    <LogIn className="h-4 w-4" />
                  </Link>
                </Button>

                {/* Sign Up button - text on desktop, icon on mobile */}
                <Button className="hidden sm:flex text-sm" asChild>
                  <Link href="/signup">Sign Up</Link>
                </Button>
                <Button size="icon" className="sm:hidden" asChild>
                  <Link href="/signup" aria-label="Sign Up">
                    <UserPlus className="h-4 w-4" />
                  </Link>
                </Button>
              </>
            )}
          </div>
        </nav>
      </div>
    </header>
  );
}
