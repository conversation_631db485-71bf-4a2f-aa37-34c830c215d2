"use client";

import React, { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Play, RefreshCw } from "lucide-react";

// Define types for video files
interface VideoFile {
  filename: string;
  path: string;
  size: number;
  last_modified: number;
}

interface VideoListResponse {
  videos: VideoFile[];
  input_directory: string;
}

const PoseDetection = () => {
  // Refs for DOM elements
  const videoRef = useRef<HTMLVideoElement>(null);

  // State variables
  const [availableVideos, setAvailableVideos] = useState<VideoFile[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [processedVideoUrl, setProcessedVideoUrl] = useState<string | null>(
    null
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available videos from the backend
  const fetchVideos = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/list-videos");

      if (!response.ok) {
        throw new Error(`Failed to fetch videos: ${response.statusText}`);
      }

      const data: VideoListResponse = await response.json();
      setAvailableVideos(data.videos);

      // If there are videos, select the first one by default
      if (data.videos.length > 0) {
        setSelectedVideo(data.videos[0].filename);
      }
    } catch (err) {
      console.error("Error fetching videos:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch videos");
    } finally {
      setIsLoading(false);
    }
  };

  // Process the selected video
  const processVideo = async () => {
    if (!selectedVideo) {
      setError("Please select a video first");
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      // Call the API to process the video
      const response = await fetch("/api/process-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filename: selectedVideo,
          profile_id: "demo",
          video_type: "demo",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process video");
      }

      const data = await response.json();

      if (data.task_id) {
        // Start polling for the processed video
        pollForProcessedVideo(data.task_id);
      } else {
        throw new Error("No task ID returned from server");
      }
    } catch (err) {
      console.error("Error processing video:", err);
      setError(err instanceof Error ? err.message : "Failed to process video");
      setIsProcessing(false);
    }
  };

  // Poll for the processed video
  const pollForProcessedVideo = async (id: string) => {
    try {
      const response = await fetch(`/api/video-status?taskId=${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to check video status");
      }

      const data = await response.json();

      if (data.processed_video_url) {
        // Video is ready
        setProcessedVideoUrl(data.processed_video_url);
        setIsProcessing(false);
      } else {
        // Video is still processing, poll again after a delay
        setTimeout(() => pollForProcessedVideo(id), 2000);
      }
    } catch (err) {
      console.error("Error checking video status:", err);
      setError(
        err instanceof Error ? err.message : "Failed to check video status"
      );
      setIsProcessing(false);
    }
  };

  // Refresh the list of videos
  const refreshVideos = () => {
    fetchVideos();
  };

  // Load videos when component mounts
  useEffect(() => {
    fetchVideos();
  }, []);

  // Render the component
  return (
    <div className="flex flex-col space-y-6 w-full max-w-4xl mx-auto">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-4">Pose Detection</h2>

        <div className="flex flex-col space-y-4">
          {/* Video selection */}
          <div className="flex flex-col space-y-2">
            <label htmlFor="video-select" className="text-sm font-medium">
              Select a video to process
            </label>
            <div className="flex space-x-2">
              <Select
                value={selectedVideo || ""}
                onValueChange={setSelectedVideo}
                disabled={isLoading || isProcessing}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a video" />
                </SelectTrigger>
                <SelectContent>
                  {availableVideos.map((video) => (
                    <SelectItem key={video.filename} value={video.filename}>
                      {video.filename}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={refreshVideos}
                disabled={isLoading || isProcessing}
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
                />
              </Button>
            </div>
          </div>

          {/* Process button */}
          <Button
            onClick={processVideo}
            disabled={!selectedVideo || isProcessing}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Process Video
              </>
            )}
          </Button>

          {/* Error message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
        </div>
      </Card>

      {/* Video display */}
      {processedVideoUrl && (
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">Processed Video</h3>
          <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-gray-100">
            <video
              ref={videoRef}
              src={processedVideoUrl}
              controls
              autoPlay
              loop
              className="h-full w-full object-contain"
            />
          </div>
        </Card>
      )}
    </div>
  );
};

export default PoseDetection;
