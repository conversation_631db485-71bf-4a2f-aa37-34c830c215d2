"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Video, Upload, Loader2 } from "lucide-react";

type VideoUploadProps = {
  title: string;
  description: string;
  videoSrc?: string | null;
  onVideoChange: (file: File | null, preview: string | null) => void;
  illustration?: React.ReactNode;
};

export function VideoUpload({
  title,
  description,
  videoSrc,
  onVideoChange,
  illustration,
}: VideoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (!file) return;

    setIsUploading(true);

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        onVideoChange(file, event.target.result as string);
        setIsUploading(false);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleRemove = () => {
    onVideoChange(null, null);
  };

  return (
    <div className="space-y-6">
      <div className="pb-6">
        <h2 className="text-2xl font-bold">{title}</h2>
        <p className="text-muted-foreground">{description}</p>
      </div>

      {/* Instruction Illustration */}
      {illustration && (
        <div className="border p-4 rounded-md bg-muted/30 flex items-center justify-center h-60">
          {illustration}
        </div>
      )}

      {/* Upload Card */}
      <Card className="border-2 border-dashed">
        <div className="p-6 flex flex-col items-center justify-center text-center space-y-4">
          {videoSrc ? (
            <div className="space-y-4">
              <div className="w-full md:w-64 mx-auto">
                <video
                  src={videoSrc}
                  controls
                  className="w-full h-auto rounded-md"
                />
              </div>
              <div className="flex space-x-2 justify-center">
                <Button variant="outline" size="sm" onClick={handleRemove}>
                  Remove
                </Button>
                <label>
                  <Button variant="default" size="sm" asChild>
                    <span className="flex items-center gap-1">
                      <Video className="h-4 w-4" />
                      Replace video
                    </span>
                  </Button>
                  <input
                    type="file"
                    accept="video/*"
                    className="sr-only"
                    onChange={handleFileSelect}
                  />
                </label>
              </div>
            </div>
          ) : (
            <>
              <div className="rounded-full bg-primary/10 p-6">
                <Video className="h-10 w-10 text-primary" />
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-lg">Upload Video</h3>
                <p className="text-sm text-muted-foreground">
                  Record a video or upload one from your device
                </p>
              </div>
              <label>
                <Button variant="default" asChild>
                  {isUploading ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Uploading...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Select Video
                    </span>
                  )}
                </Button>
                <input
                  type="file"
                  accept="video/*"
                  className="sr-only"
                  onChange={handleFileSelect}
                  disabled={isUploading}
                />
              </label>
            </>
          )}
        </div>
      </Card>
    </div>
  );
}
