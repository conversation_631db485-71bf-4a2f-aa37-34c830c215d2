"use client";

import Link from "next/link";
import { TypeAnimation } from "react-type-animation";
import { motion } from "framer-motion";

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export function HeroSection() {
  return (
    <motion.section
      className="relative flex items-center justify-center w-full py-20 sm:py-28 min-h-[75vh] overflow-hidden"
      initial="hidden"
      animate="visible"
      variants={staggerVariant}
    >
      {/* Hero video background - no overlays for clean color inversion */}
      <div className="absolute inset-0 z-0 w-full h-full overflow-hidden">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute h-full w-full object-cover object-center"
        >
          <source src="/videos/runner_video.mp4" type="video/mp4" />
        </video>
      </div>

      {/* Container for text positioned at the bottom and centered */}
      <div className="w-full z-10 flex flex-col items-center justify-end h-full absolute inset-x-0 bottom-0 pb-12 sm:pb-16">
        <div className="text-center w-full max-w-3xl mx-auto px-4 relative">
          {/* Text with black color */}
          <div className="relative z-20 text-center">
            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-black leading-tight text-center flex flex-col items-center gap-0"
              variants={fadeInVariant}
            >
              <div className="flex justify-center">
                <TypeAnimation
                  sequence={[
                    "Eigen",
                    2000,
                    "Precision",
                    1500,
                    "Optimized",
                    1500,
                    "Firmware",
                    1500,
                  ]}
                  wrapper="span"
                  speed={10}
                  deletionSpeed={60}
                  className="inline-block font-sans font-medium"
                  repeat={Infinity}
                />
              </div>
              <span className="font-sans font-extrabold text-4xl sm:text-5xl md:text-6xl relative -mt-1">
                for your stride
              </span>
            </motion.h1>
          </div>

          {/* Compile Your Profile Button */}
          <motion.div
            className="mt-3 sm:mt-4 flex justify-center relative z-10"
            variants={fadeInVariant}
          >
            <Link href="/signup" passHref>
              <motion.span
                className="group relative inline-flex items-center px-6 py-3 bg-primary text-primary-foreground text-sm sm:text-base font-sans font-medium rounded-md cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="flex items-center">
                  Compile Your Profile
                  <span className="ml-1">&gt;</span>
                </span>
              </motion.span>
            </Link>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
