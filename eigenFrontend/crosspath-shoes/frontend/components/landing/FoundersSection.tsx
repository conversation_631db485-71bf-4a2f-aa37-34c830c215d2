"use client";

import Image from "next/image";
import { motion } from "framer-motion";

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export function FoundersSection() {
  return (
    <motion.section
      className="relative py-10 sm:py-12 bg-secondary"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
      variants={staggerVariant}
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 z-10 relative">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <motion.div
            variants={fadeInVariant}
            className="order-2 md:order-1"
          >
            <div className="bg-white border border-border/30 rounded-lg shadow-md overflow-hidden relative">
              <div className="relative">
                <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-border/30">
                  <div className="flex space-x-1.5">
                    <span className="w-2.5 h-2.5 bg-red-400 rounded-full"></span>
                    <span className="w-2.5 h-2.5 bg-yellow-400 rounded-full"></span>
                    <span className="w-2.5 h-2.5 bg-green-400 rounded-full"></span>
                  </div>
                  <p className="text-xs text-gray-500 font-mono">
                    eigen_founders.js
                  </p>
                </div>

                <div className="p-4 text-sm text-left overflow-x-auto font-input relative">
                  <pre className="whitespace-pre-wrap relative z-10">
                    <code className="language-javascript text-gray-800 block">{`// About Us

// founders
Till Findl (UCL Medicine)
Max Krause (ETH Zurich Robotics)

// problem
Met on a run in London and ran into the same problem:
finding the right running shoe felt like guesswork.
Too many options. Too much branding. No link to how
you actually move.

// solution
We built eigenFIT: personalised shoe recommendations
based on your stride. Like a running store and lab
analysis in your pocket.`}</code>
                  </pre>

                  {/* Simple cursor animation */}
                  <motion.div
                    className="absolute bottom-4 left-[calc(8rem)] w-1.5 h-4 bg-primary/70"
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  ></motion.div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            variants={fadeInVariant}
            className="order-1 md:order-2"
          >
            <Image
              src="/images/eigen_founders.jpg"
              alt="Eigen Founders"
              width={500}
              height={400}
              className="rounded-lg shadow-md object-cover w-full"
            />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
