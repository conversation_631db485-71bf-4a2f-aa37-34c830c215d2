"use client";

import Link from "next/link";
import { motion } from "framer-motion";

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export function CTASection() {
  return (
    <motion.section
      className="relative w-full py-10 sm:py-12 overflow-hidden"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={staggerVariant}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 text-center z-10 relative">
        <motion.div
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
          }}
          className="p-6 sm:p-8 md:p-10 bg-white rounded-lg border border-border/30 shadow-md relative overflow-hidden"
        >
          <div className="relative">
            <motion.h2
              className="text-4xl sm:text-5xl md:text-6xl font-sans font-semibold tracking-tight text-foreground max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              Ready to{" "}
              <span className="text-primary relative inline-block">
                compile
                <motion.span
                  className="absolute -bottom-1 left-0 w-full h-1 bg-primary/50"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.5, duration: 1, ease: "easeOut" }}
                ></motion.span>
              </span>{" "}
              your <br className="sm:hidden" />
              perfect shoe match?
            </motion.h2>

            <motion.p
              className="mt-4 sm:mt-5 md:mt-6 text-base sm:text-lg text-foreground/90 font-jetbrains max-w-xl mx-auto leading-relaxed p-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              viewport={{ once: true }}
            >
              &gt; ~5 minutes input for personalized, data-driven
              recommendations.
            </motion.p>

            <motion.div
              className="mt-6 sm:mt-8"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Link href="/signup" passHref>
                <motion.span
                  className="group relative inline-flex items-center px-8 py-4 bg-primary text-primary-foreground text-base sm:text-lg font-sans font-medium rounded-md cursor-pointer shadow-md transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="relative z-10 flex items-center">
                    Start Free Analysis
                    <span className="ml-1">&gt;</span>
                  </span>
                </motion.span>
              </Link>
            </motion.div>

            <motion.p
              className="mt-6 sm:mt-8 text-xs sm:text-sm text-foreground/60 font-input italic"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <span className="text-primary/80 mr-2">//</span> Built by
              engineers & biomechanists. Not marketers.
            </motion.p>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
