import React from "react";

export function RunningVideoGuidance({ viewType }: { viewType: 'sagittal' | 'posterior' }) {
  // Customize guidance based on viewType
  return (
    <div className="mb-4 p-4 border rounded-md bg-muted text-muted-foreground text-sm">
      <h4 className="font-semibold mb-2">How to record the video ({viewType}):</h4>
      <ul className="list-disc pl-5 space-y-1">
        {viewType === 'sagittal' && (
          <>
            <li>Record from the side view.</li>
            <li>Ensure your whole body is visible.</li>
            <li>Run at a steady pace on a treadmill or flat surface.</li>
            <li>Record for at least 10 seconds.</li>
          </>
        )}
        {viewType === 'posterior' && (
          <>
            <li>Record from directly behind.</li>
            <li>Ensure your whole body is visible, especially feet and legs.</li>
            <li>Run at a steady pace on a treadmill or flat surface.</li>
            <li>Record for at least 10 seconds.</li>
          </>
        )}
        <li>Ensure good lighting.</li>
      </ul>
    </div>
  );
} 