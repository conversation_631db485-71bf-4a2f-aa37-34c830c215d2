import { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { z } from "zod";
import { Eye, EyeOff, Loader2 } from "lucide-react";

// Form validation schema
const signupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type SignupForm = z.infer<typeof signupSchema>;

export default function SignUpForm() {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<SignupForm>({
    name: "",
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [success, setSuccess] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when typing
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({}); // Clear previous errors
    setSuccess(null); // Clear previous success message

    try {
      // Client-side validation using Zod
      const validatedData = signupSchema.parse(formData);

      console.log("Attempting to sign up with:", validatedData.email);

      // Sign up with Supabase Auth
      const supabase = createClient();
      const { data, error } = await supabase.auth.signUp({
        email: validatedData.email,
        password: validatedData.password,
        options: {
          data: {
            name: validatedData.name,
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      console.log("Sign up response:", {
        user: data?.user ? { id: data.user.id, email: data.user.email } : null,
        error,
      });

      if (error) {
        console.error("Sign up error:", error);
        throw new Error(
          error.message || "Registration failed. Please try again."
        );
      }

      // Create a User record
      if (data.user) {
        console.log("Creating User record for user:", data.user.id);
        try {
          const { data: userData, error: userError } = await supabase
            .from("User")
            .insert([
              {
                id: data.user.id,
                name: validatedData.name,
                email: validatedData.email,
              },
            ])
            .select()
            .single();

          if (userError) {
            console.error("Error creating User record:", userError);
          } else {
            console.log("User record created successfully:", userData);
          }
        } catch (userError) {
          console.error("Error creating User record:", userError);
        }
      }

      // Show success message
      setSuccess(
        "Account created! Please check your email for a verification link. " +
          "You'll be able to sign in after verifying your email."
      );
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Handle Zod validation errors
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      } else if (error instanceof Error) {
        // Handle API or other general errors
        setErrors({ form: error.message });
      } else {
        // Fallback error message
        setErrors({ form: "An unexpected error occurred. Please try again." });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Success message */}
      {success && (
        <div className="bg-green-500/10 dark:bg-green-500/10 border border-green-500/30 dark:border-green-500/30 text-green-700 dark:text-green-400 text-xs p-3 rounded-sm font-mono">
          {success}
        </div>
      )}

      {/* Error message */}
      {errors.form && (
        <div className="bg-red-500/10 dark:bg-syntax-error/10 border border-red-500/30 dark:border-syntax-error/30 text-red-700 dark:text-syntax-error text-xs p-3 rounded-sm font-mono">
          {errors.form}
        </div>
      )}

      <div className="space-y-1.5">
        <Label
          htmlFor="name"
          className="text-xs font-medium text-brand-black/70 dark:text-text-main/70"
        >
          Name
        </Label>
        <Input
          id="name"
          name="name"
          placeholder="Ada Lovelace"
          type="text"
          autoCapitalize="none"
          autoComplete="name"
          autoCorrect="off"
          value={formData.name}
          onChange={handleChange}
          disabled={loading}
          className="themed-input"
        />
        {errors.name && (
          <p className="text-red-700 dark:text-syntax-error text-xs mt-1 font-mono">
            {errors.name}
          </p>
        )}
      </div>

      <div className="space-y-1.5">
        <Label
          htmlFor="email"
          className="text-xs font-medium text-brand-black/70 dark:text-text-main/70"
        >
          Email
        </Label>
        <Input
          id="email"
          name="email"
          placeholder="<EMAIL>"
          type="email"
          autoCapitalize="none"
          autoComplete="email"
          autoCorrect="off"
          value={formData.email}
          onChange={handleChange}
          disabled={loading}
          className="themed-input"
        />
        {errors.email && (
          <p className="text-red-700 dark:text-syntax-error text-xs mt-1 font-mono">
            {errors.email}
          </p>
        )}
      </div>

      <div className="space-y-1.5">
        <Label
          htmlFor="password"
          className="text-xs font-medium text-brand-black/70 dark:text-text-main/70"
        >
          Password
        </Label>
        <div className="relative">
          <Input
            id="password"
            name="password"
            placeholder=">********"
            type={showPassword ? "text" : "password"}
            autoCapitalize="none"
            autoComplete="new-password"
            value={formData.password}
            onChange={handleChange}
            disabled={loading}
            className="themed-input pr-10"
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1.5 top-1/2 -translate-y-1/2 h-7 w-7 p-0 text-brand-black/50 dark:text-text-main/50 hover:text-brand-black dark:hover:text-text-main hover:bg-zinc-100/50 dark:hover:bg-sweetspot-black/50"
            onClick={() => setShowPassword(!showPassword)}
            disabled={loading}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
            <span className="sr-only">
              {showPassword ? "Hide password" : "Show password"}
            </span>
          </Button>
        </div>
        {errors.password && (
          <p className="text-red-700 dark:text-syntax-error text-xs mt-1 font-mono">
            {errors.password}
          </p>
        )}
      </div>

      <Button
        type="submit"
        className="w-full bg-brand-black text-off-white dark:bg-off-white dark:text-text-inverted hover:bg-brand-black/90 dark:hover:bg-off-white/90 font-sans text-sm h-9 group relative overflow-hidden transition-colors duration-300 disabled:opacity-60"
        disabled={loading}
      >
        {loading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Compiling...
          </>
        ) : (
          <span className="relative z-10">Sign up &gt;</span>
        )}
      </Button>
    </form>
  );
}
