"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON>lider } from "@/components/ui/slider"
import { cn } from "@/lib/utils"

interface CustomizationOptions {
  color: string
  size: number
  width: "narrow" | "regular" | "wide"
  cushioning: "minimal" | "balanced" | "maximum"
}

const colors = [
  { name: "Blue", value: "#2563eb" },
  { name: "Red", value: "#dc2626" },
  { name: "Green", value: "#16a34a" },
  { name: "Purple", value: "#9333ea" },
  { name: "Orange", value: "#ea580c" },
  { name: "Black", value: "#171717" },
]

export function ShoeCustomizer() {
  const [customizations, setCustomizations] = useState<CustomizationOptions>({
    color: "#2563eb",
    size: 42,
    width: "regular",
    cushioning: "balanced",
  })

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <div>
          <Label>Color</Label>
          <div className="grid grid-cols-6 gap-2 mt-2">
            {colors.map((color) => (
              <button
                key={color.value}
                className={cn(
                  "h-8 w-8 rounded-full border-2",
                  customizations.color === color.value
                    ? "border-primary"
                    : "border-transparent"
                )}
                style={{ backgroundColor: color.value }}
                onClick={() =>
                  setCustomizations({ ...customizations, color: color.value })
                }
                title={color.name}
              />
            ))}
          </div>
        </div>

        <div>
          <Label>Size (EU)</Label>
          <div className="mt-2">
            <Slider
              value={[customizations.size]}
              min={36}
              max={47}
              step={0.5}
              onValueChange={([size]) =>
                setCustomizations({ ...customizations, size })
              }
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>36</span>
              <span className="font-medium text-foreground">
                {customizations.size}
              </span>
              <span>47</span>
            </div>
          </div>
        </div>

        <div>
          <Label>Width</Label>
          <RadioGroup
            value={customizations.width}
            onValueChange={(width: "narrow" | "regular" | "wide") =>
              setCustomizations({ ...customizations, width })
            }
            className="grid grid-cols-3 gap-4 mt-2"
          >
            <div>
              <RadioGroupItem
                value="narrow"
                id="width-narrow"
                className="peer sr-only"
              />
              <Label
                htmlFor="width-narrow"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Narrow</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="regular"
                id="width-regular"
                className="peer sr-only"
              />
              <Label
                htmlFor="width-regular"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Regular</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="wide"
                id="width-wide"
                className="peer sr-only"
              />
              <Label
                htmlFor="width-wide"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Wide</span>
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div>
          <Label>Cushioning</Label>
          <RadioGroup
            value={customizations.cushioning}
            onValueChange={(cushioning: "minimal" | "balanced" | "maximum") =>
              setCustomizations({ ...customizations, cushioning })
            }
            className="grid grid-cols-3 gap-4 mt-2"
          >
            <div>
              <RadioGroupItem
                value="minimal"
                id="cushioning-minimal"
                className="peer sr-only"
              />
              <Label
                htmlFor="cushioning-minimal"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Minimal</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="balanced"
                id="cushioning-balanced"
                className="peer sr-only"
              />
              <Label
                htmlFor="cushioning-balanced"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Balanced</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="maximum"
                id="cushioning-maximum"
                className="peer sr-only"
              />
              <Label
                htmlFor="cushioning-maximum"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Maximum</span>
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="outline">Reset</Button>
        <Button>Save Design</Button>
      </div>
    </div>
  )
} 