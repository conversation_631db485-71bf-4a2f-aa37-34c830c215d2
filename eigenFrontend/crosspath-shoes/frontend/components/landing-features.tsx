import { Activity, ScanFace, Footprints, Ruler, CloudLightning, ShoppingBag } from "lucide-react"

export function LandingFeatures() {
  return (
    <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">Features</div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight text-primary">
              Advanced Technology for Perfect Fit
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Our cutting-edge platform combines 3D scanning, athletic data, and machine learning to create shoes
              perfectly tailored to your unique needs.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-3 lg:gap-12">
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <ScanFace className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">3D Foot Scanning</h3>
            <p className="text-muted-foreground">
              Advanced scanning technology captures the unique shape and structure of your feet for perfect fit.
            </p>
          </div>
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <Activity className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Athletic Data Integration</h3>
            <p className="text-muted-foreground">
              Sync with your favorite fitness apps to incorporate your running metrics into shoe design.
            </p>
          </div>
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <CloudLightning className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">ML-Powered Design</h3>
            <p className="text-muted-foreground">
              Our algorithms analyze your data to recommend the perfect shoe specifications for your unique needs.
            </p>
          </div>
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <Ruler className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Custom Specifications</h3>
            <p className="text-muted-foreground">
              Tailor every aspect of your shoes from cushioning to support based on your preferences.
            </p>
          </div>
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <Footprints className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Mileage Tracking</h3>
            <p className="text-muted-foreground">
              Monitor your shoe&apos;s lifespan and receive timely alerts when it&apos;s time for a replacement.
            </p>
          </div>
          <div className="grid gap-4 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mx-auto">
              <ShoppingBag className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Seamless Ordering</h3>
            <p className="text-muted-foreground">
              Preview your custom design in 3D before placing your order with our secure checkout process.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

