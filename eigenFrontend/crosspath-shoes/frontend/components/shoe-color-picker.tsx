"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface ShoeColorPickerProps {
  onColorChange?: (color: string) => void
}

const colorOptions = [
  { id: "black", name: "Black", value: "#000000" },
  { id: "white", name: "White", value: "#FFFFFF" },
  { id: "gray", name: "Gray", value: "#808080" },
  { id: "blue", name: "Blue", value: "#2563eb" },
  { id: "green", name: "Green", value: "#16a34a" },
  { id: "red", name: "Red", value: "#dc2626" },
]

export function ShoeColorPicker({ onColorChange }: ShoeColorPickerProps) {
  const [primaryColor, setPrimaryColor] = useState("blue")

  const handleColorChange = (colorId: string) => {
    setPrimaryColor(colorId)
    const color = colorOptions.find(c => c.id === colorId)
    if (color && onColorChange) {
      onColorChange(color.value)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-3">Primary Color</h3>
        <RadioGroup value={primaryColor} onValueChange={handleColorChange} className="flex flex-wrap gap-3">
          {colorOptions.map((color) => (
            <div key={color.id} className="flex items-center space-x-2">
              <RadioGroupItem value={color.id} id={`primary-${color.id}`} className="peer sr-only" />
              <Label
                htmlFor={`primary-${color.id}`}
                className="flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <div className="w-8 h-8 rounded-full border" style={{ backgroundColor: color.value }} />
                <span className="mt-1 text-xs">{color.name}</span>
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    </div>
  )
}

