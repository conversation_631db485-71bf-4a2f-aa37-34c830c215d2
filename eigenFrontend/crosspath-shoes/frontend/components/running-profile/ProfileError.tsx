import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, AlertTriangle } from "lucide-react";

interface ProfileErrorProps {
  error: string | null;
}

export function ProfileError({ error }: ProfileErrorProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center mb-4">
        <Link href="/running-profiles">
          <Button
            variant="outline"
            size="sm"
            className="inline-flex items-center gap-1.5"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Back to Profiles</span>
          </Button>
        </Link>
      </div>

      <div className="rounded-xl bg-destructive/10 p-8 text-destructive flex flex-col items-center text-center sm:text-left sm:flex-row sm:items-start gap-6 border border-destructive/20">
        <div className="w-12 h-12 rounded-full bg-destructive/20 flex items-center justify-center">
          <AlertTriangle className="h-6 w-6 flex-shrink-0" />
        </div>
        <div>
          <h3 className="text-lg font-medium mb-2">Error Loading Profile</h3>
          <p className="text-destructive/80">
            {error || "Profile not found or could not be loaded"}
          </p>
          <div className="mt-4">
            <Link href="/running-profiles">
              <Button
                variant="outline"
                className="border-destructive/30 hover:bg-destructive/10"
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Return to Profiles
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
