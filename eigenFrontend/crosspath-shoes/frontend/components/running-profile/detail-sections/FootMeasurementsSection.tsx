"use client";

import React from "react";
import { RunningProfile } from "@/lib/types";
import { Footprints, Ruler, Download, ChevronDown, Info } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { StorageMedia } from "@/components/ui/storage-media";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FootMeasurementsSectionProps {
  profile: RunningProfile;
}

const DataItem: React.FC<{
  label: string;
  value: string | number | null | undefined;
  unit?: string;
  icon?: React.ElementType;
  explanation?: string;
}> = ({ label, value, unit, icon: Icon, explanation }) => {
  return (
    <div className="flex items-start space-x-2 p-2 bg-background hover:bg-muted/50 rounded-md transition-colors duration-150 min-h-[56px]">
      {Icon && <Icon className={`h-4 w-4 mt-0.5 text-primary flex-shrink-0`} />}
      <div className="flex-grow">
        <div className="flex items-center space-x-1">
          <p className="text-xs font-medium text-muted-foreground">{label}</p>
          {explanation && (
            <Tooltip delayDuration={100}>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent
                side="top"
                className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs"
              >
                <p>{explanation}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <p className={`text-sm font-semibold text-foreground break-words`}>
          {value ? `${value}${unit || ""}` : "N/A"}
        </p>
      </div>
    </div>
  );
};

const FootScanDisplay: React.FC<{
  scanUrl: string | null | undefined;
  label: string;
}> = ({ scanUrl, label }) => {
  if (!scanUrl) return null;
  return (
    <div className="mt-2">
      <p className="text-xs font-medium text-muted-foreground mb-1">{label}:</p>
      <div className="aspect-[3/4] w-full max-w-[180px] mx-auto bg-muted rounded-md overflow-hidden border border-border shadow-sm">
        <StorageMedia
          storagePath={scanUrl}
          type="image"
          aspectRatio="custom"
          className="object-contain w-full h-full"
        />
      </div>
      <Button
        variant="outline"
        size="sm"
        asChild
        className="mt-1.5 w-full border-primary/30 text-primary/80 hover:bg-primary/5 hover:text-primary text-xs"
      >
        <a href={scanUrl} target="_blank" rel="noopener noreferrer">
          <Download className="mr-1.5 h-3 w-3" />
          View Scan
        </a>
      </Button>
    </div>
  );
};

export function FootMeasurementsSection({
  profile,
}: FootMeasurementsSectionProps) {
  return (
    <TooltipProvider>
      <Accordion
        type="single"
        collapsible
        defaultValue="foot-measurements"
        className="w-full"
      >
        <AccordionItem value="foot-measurements" className="border-none">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 bg-card hover:bg-muted/50 rounded-lg shadow-sm transition-colors duration-150 group hover:no-underline focus-visible:no-underline">
            <div className="flex items-center space-x-2.5">
              <Footprints className="h-5 w-5 text-primary" />
              <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors duration-150 no-underline group-hover:no-underline">
                Foot Structure & Measurements
              </h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-0 px-0.5 pb-0.5">
            <div className="border-t border-border dark:border-zinc-700/70 mt-1.5 mb-0.5"></div>
            <Card className="bg-transparent border-none shadow-none">
              <CardContent className="p-2 space-y-3">
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  <DataItem
                    label="Heel-to-Toe Length (L)"
                    value={profile.heelToToeLengthLeft}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Standard measurement of foot length from the heel to the longest toe."
                  />
                  <DataItem
                    label="Heel-to-Toe Length (R)"
                    value={profile.heelToToeLengthRight}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Standard measurement of foot length from the heel to the longest toe."
                  />
                  <DataItem
                    label="Forefoot Width (L)"
                    value={profile.forefootWidthLeft}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Measurement of the widest part of the foot, across the metatarsal heads."
                  />
                  <DataItem
                    label="Forefoot Width (R)"
                    value={profile.forefootWidthRight}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Measurement of the widest part of the foot, across the metatarsal heads."
                  />
                  <DataItem
                    label="Medial Arch Length (L)"
                    value={profile.medialArchLengthLeft}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Length of the medial (inner) arch of the foot."
                  />
                  <DataItem
                    label="Medial Arch Length (R)"
                    value={profile.medialArchLengthRight}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Length of the medial (inner) arch of the foot."
                  />
                  <DataItem
                    label="Medial Arch Height (L)"
                    value={profile.medialArchHeightLeft}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Height of the medial (inner) arch, indicating arch type."
                  />
                  <DataItem
                    label="Medial Arch Height (R)"
                    value={profile.medialArchHeightRight}
                    unit=" mm"
                    icon={Ruler}
                    explanation="Height of the medial (inner) arch, indicating arch type."
                  />
                </div>

                {(profile.footImageTopLeftUrl ||
                  profile.footImageTopRightUrl ||
                  profile.footImageMedialLeftUrl ||
                  profile.footImageMedialRightUrl) && (
                  <div className="pt-2">
                    <h4 className="text-sm font-semibold text-foreground mb-1.5">
                      Foot Scans/Images
                    </h4>
                    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3">
                      <FootScanDisplay
                        scanUrl={profile.footImageTopLeftUrl}
                        label="Top View (L)"
                      />
                      <FootScanDisplay
                        scanUrl={profile.footImageTopRightUrl}
                        label="Top View (R)"
                      />
                      <FootScanDisplay
                        scanUrl={profile.footImageMedialLeftUrl}
                        label="Medial View (L)"
                      />
                      <FootScanDisplay
                        scanUrl={profile.footImageMedialRightUrl}
                        label="Medial View (R)"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </TooltipProvider>
  );
}
