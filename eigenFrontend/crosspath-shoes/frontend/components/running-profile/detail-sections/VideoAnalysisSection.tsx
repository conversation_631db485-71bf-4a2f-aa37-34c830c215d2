"use client";

import React, { useState } from "react";
import { RunningProfile } from "@/lib/types";
import {
  Video,
  ChevronDown,
  Brain,
  Download,
  AlertTriangle,
  Info,
  Activity,
  ArrowDown,
  MoveVertical,
  Ruler as RulerIcon,
  Footprints as FootprintsIcon,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { StorageMedia } from "@/components/ui/storage-media";
import { Button } from "@/components/ui/button";
import { TypeAnimation } from "react-type-animation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AnalyzeVideoButton } from "../AnalyzeVideoButton";

interface VideoAnalysisSectionProps {
  profile: RunningProfile;
}

const dataPointExplanations: Record<string, string> = {
  "Pelvic Drop":
    "Pelvic tilt during stance phase; excessive drop may indicate hip abductor weakness.",
  "Knee Drift":
    "Lateral knee movement during stance; significant drift can indicate instability.",
  "Foot Drift/Crossover":
    "Lateral foot deviation from a neutral path, indicating crossover or excessive width.",
  "Heel Whip":
    "Circular heel motion during swing phase as the foot leaves the ground.",
  "Posterior Stability Score":
    "Composite score for stability and control from posterior view; higher is better.",
  Overstride:
    "Landing with the foot too far ahead of the body's center of mass.",
  "Ankle Dorsiflexion":
    "The upward movement of the foot towards the shin at key points like landing or toe-off.",
  "Ankle Plantarflexion":
    "The downward movement of the foot, pointing the toes, typically at push-off.",
  "Vertical Oscillation (Video)":
    "The vertical 'bounce' of your center of mass during running, as measured from video analysis.",
  "Trunk Lean":
    "Forward or backward lean of the torso; a slight forward lean is often preferred.",
  "Knee Flexion (Loading)":
    "Knee bend amount during initial stance phase as the body absorbs impact.",
};

const DataPoint: React.FC<{
  label: string;
  value: string | number | null | undefined;
  unit?: string;
  icon?: React.ElementType;
  explanation?: string;
}> = ({ label, value, unit, icon: Icon, explanation }) => (
  <div
    className={`p-2 rounded-md transition-colors duration-150 bg-background hover:bg-muted/50 min-h-[56px]`}
  >
    <div className="flex items-center space-x-1 mb-0.5">
      {Icon && <Icon className={`h-3.5 w-3.5 text-primary flex-shrink-0`} />}
      <p className={`text-xs font-medium text-muted-foreground`}>{label}</p>
      {explanation && (
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <Info className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent
            side="top"
            className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs"
          >
            <p>{explanation}</p>
          </TooltipContent>
        </Tooltip>
      )}
    </div>
    <p className={`text-sm font-semibold text-foreground break-words`}>
      {value ? `${value}${unit || ""}` : "N/A"}
    </p>
  </div>
);

const VideoPlayer: React.FC<{
  videoUrl: string | null | undefined;
  label: string;
}> = ({ videoUrl, label }) => {
  if (!videoUrl) return null;
  return (
    <div className="mt-1.5 flex flex-col h-full">
      <p className="text-xs font-medium text-muted-foreground mb-1">{label}:</p>
      <div className="flex-grow aspect-[9/14] sm:aspect-[2/3] w-full bg-muted rounded-md overflow-hidden border border-border shadow-sm">
        <StorageMedia
          storagePath={videoUrl}
          type="video"
          aspectRatio="custom"
          className="object-cover w-full h-full"
          showFakeDataLabel={false}
          loopPreview={true}
        />
      </div>
      <Button
        variant="outline"
        size="sm"
        asChild
        className="mt-1.5 w-full border-primary/30 text-primary/80 hover:bg-primary/5 hover:text-primary text-xs"
      >
        <a href={videoUrl} target="_blank" rel="noopener noreferrer">
          <Download className="mr-1.5 h-3 w-3" />
          View/Download
        </a>
      </Button>
    </div>
  );
};

export function VideoAnalysisSection({ profile }: VideoAnalysisSectionProps) {
  const [hasTyped, setHasTyped] = useState(false);

  const keyInsight =
    profile.gaitAnalysisSummary ||
    "Video analysis pending or no specific summary generated.";

  let criticalObservationsArray: Array<{
    metric: string;
    value: string;
    normalRange: string;
    severity: string;
  }> = [];
  if (profile.criticalObservationsJson) {
    try {
      criticalObservationsArray = JSON.parse(
        profile.criticalObservationsJson as string
      );
    } catch (error) {
      console.error("Failed to parse criticalObservationsJson:", error);
      // criticalObservationsArray remains empty
    }
  }

  const hasVideoAnalysisData =
    profile.pelvicDrop !== null ||
    profile.kneeDrift !== null ||
    profile.footDrift !== null ||
    profile.heelWhip !== null ||
    profile.posteriorStabilityScore !== null ||
    profile.overstride !== null ||
    profile.ankleDorsiflexion !== null ||
    profile.anklePlantarflexion !== null ||
    profile.verticalOscillationVideo !== null ||
    profile.trunkLean !== null ||
    profile.kneeFlexionLoading !== null;

  return (
    <TooltipProvider>
      <Accordion
        type="single"
        collapsible
        defaultValue="video-analysis"
        className="w-full"
      >
        <AccordionItem value="video-analysis" className="border-none">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 bg-card hover:bg-muted/50 rounded-lg shadow-sm transition-colors duration-150 group hover:no-underline focus-visible:no-underline">
            <div className="flex items-center space-x-2.5">
              <Video className="h-5 w-5 text-primary" />
              <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors duration-150 no-underline group-hover:no-underline">
                Video Gait Analysis
              </h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-0 px-0.5 pb-0.5">
            <div className="border-t border-border dark:border-zinc-700/70 mt-1.5 mb-0.5"></div>
            <Card className="bg-transparent border-none shadow-none">
              <CardContent className="p-2 space-y-3">
                {profile.gaitAnalysisSummary && (
                  <Alert className="mb-3 bg-primary/5 border-primary/20 p-3">
                    <Brain className="h-4 w-4 text-primary" />
                    <AlertTitle className="font-semibold text-primary text-sm mb-0.5">
                      AI Generated Insight
                    </AlertTitle>
                    <AlertDescription className="text-primary/80 min-h-[34px] text-xs">
                      {!hasTyped ? (
                        <TypeAnimation
                          sequence={[keyInsight, () => setHasTyped(true)]}
                          wrapper="span"
                          speed={70}
                          cursor={true}
                          repeat={0}
                          style={{ display: "inline-block" }}
                        />
                      ) : (
                        keyInsight
                      )}
                    </AlertDescription>
                  </Alert>
                )}

                {criticalObservationsArray.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-sm font-semibold text-foreground mb-1.5 flex items-center">
                      <AlertTriangle className="h-4 w-4 text-destructive mr-1.5" />{" "}
                      Critical Observations
                    </h4>
                    <div className="space-y-1.5">
                      {criticalObservationsArray.map((obs, index) => {
                        const explanation =
                          dataPointExplanations[obs.metric] ||
                          "Detailed information about this metric.";
                        return (
                          <Alert
                            key={index}
                            variant="destructive"
                            className="bg-destructive/5 border-destructive/20 p-2.5"
                          >
                            <AlertTriangle className="h-3.5 w-3.5" />
                            <AlertTitle className="text-xs font-medium flex items-center">
                              {obs.metric}:
                              {explanation && (
                                <Tooltip delayDuration={100}>
                                  <TooltipTrigger asChild>
                                    <Info className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-help ml-1" />
                                  </TooltipTrigger>
                                  <TooltipContent
                                    side="top"
                                    className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs"
                                  >
                                    <p>{explanation}</p>
                                  </TooltipContent>
                                </Tooltip>
                              )}
                              <span className="font-bold ml-1.5">
                                {obs.value}
                              </span>
                            </AlertTitle>
                            <AlertDescription className="text-xs pl-[calc(0.875rem+0.25rem+0.375rem)]">
                              (Normal Range: {obs.normalRange}) - Severity:{" "}
                              {obs.severity}
                            </AlertDescription>
                          </Alert>
                        );
                      })}
                    </div>
                  </div>
                )}

                {!hasVideoAnalysisData &&
                  !profile.gaitAnalysisSummary &&
                  criticalObservationsArray.length === 0 && (
                    <p className="text-xs text-muted-foreground text-center py-3">
                      No detailed video analysis data currently available for
                      this profile.
                    </p>
                  )}

                {hasVideoAnalysisData && (
                  <>
                    <h4 className="text-sm font-semibold text-foreground mb-1.5 pt-1">
                      Key Biomechanical Metrics
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {profile.pelvicDrop !== null && (
                        <DataPoint
                          label="Pelvic Drop"
                          value={profile.pelvicDrop}
                          unit="°"
                          icon={Activity}
                          explanation={dataPointExplanations["Pelvic Drop"]}
                        />
                      )}
                      {profile.kneeDrift !== null && (
                        <DataPoint
                          label="Knee Drift"
                          value={profile.kneeDrift}
                          unit="cm"
                          icon={Activity}
                          explanation={dataPointExplanations["Knee Drift"]}
                        />
                      )}
                      {profile.footDrift !== null && (
                        <DataPoint
                          label="Foot Drift/Crossover"
                          value={profile.footDrift}
                          unit="cm"
                          icon={FootprintsIcon}
                          explanation={
                            dataPointExplanations["Foot Drift/Crossover"]
                          }
                        />
                      )}
                      {profile.heelWhip !== null && (
                        <DataPoint
                          label="Heel Whip"
                          value={profile.heelWhip}
                          unit="cm"
                          icon={FootprintsIcon}
                          explanation={dataPointExplanations["Heel Whip"]}
                        />
                      )}
                      {profile.posteriorStabilityScore !== null && (
                        <DataPoint
                          label="Posterior Stability Score"
                          value={profile.posteriorStabilityScore}
                          unit="/100"
                          icon={Activity}
                          explanation={
                            dataPointExplanations["Posterior Stability Score"]
                          }
                        />
                      )}
                      {profile.overstride !== null && (
                        <DataPoint
                          label="Overstride"
                          value={profile.overstride}
                          unit="cm"
                          icon={RulerIcon}
                          explanation={dataPointExplanations["Overstride"]}
                        />
                      )}
                      {profile.ankleDorsiflexion !== null && (
                        <DataPoint
                          label="Ankle Dorsiflexion"
                          value={profile.ankleDorsiflexion}
                          unit="°"
                          icon={ArrowDown}
                          explanation={
                            dataPointExplanations["Ankle Dorsiflexion"]
                          }
                        />
                      )}
                      {profile.anklePlantarflexion !== null && (
                        <DataPoint
                          label="Ankle Plantarflexion"
                          value={profile.anklePlantarflexion}
                          unit="°"
                          icon={ArrowDown}
                          explanation={
                            dataPointExplanations["Ankle Plantarflexion"]
                          }
                        />
                      )}
                      {profile.verticalOscillationVideo !== null && (
                        <DataPoint
                          label="Vertical Oscillation (Video)"
                          value={profile.verticalOscillationVideo}
                          unit="cm"
                          icon={MoveVertical}
                          explanation={
                            dataPointExplanations[
                              "Vertical Oscillation (Video)"
                            ]
                          }
                        />
                      )}
                      {profile.trunkLean !== null && (
                        <DataPoint
                          label="Trunk Lean"
                          value={profile.trunkLean}
                          unit="°"
                          icon={Activity}
                          explanation={dataPointExplanations["Trunk Lean"]}
                        />
                      )}
                      {profile.kneeFlexionLoading !== null && (
                        <DataPoint
                          label="Knee Flexion (Loading)"
                          value={profile.kneeFlexionLoading}
                          unit="°"
                          icon={Activity}
                          explanation={
                            dataPointExplanations["Knee Flexion (Loading)"]
                          }
                        />
                      )}
                    </div>
                  </>
                )}

                {(profile.runningVideoSagittalUrl ||
                  profile.runningVideoPosteriorUrl) && (
                  <div className="pt-2">
                    <div className="flex items-center justify-between mb-1.5">
                      <h4 className="text-sm font-semibold text-foreground">
                        Submitted Videos
                      </h4>
                      {/* Show analyze button if sagittal video exists but no analysis data */}
                      {profile.runningVideoSagittalUrl && !hasVideoAnalysisData && (
                        <AnalyzeVideoButton
                          profileId={profile.id}
                          videoUrl={profile.runningVideoSagittalUrl}
                          videoType="sagittal"
                          hasAnalysisData={hasVideoAnalysisData}
                          className="text-xs px-2 py-1 h-7"
                        />
                      )}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      <VideoPlayer
                        videoUrl={profile.runningVideoSagittalUrl}
                        label="Sagittal View (Side)"
                      />
                      <VideoPlayer
                        videoUrl={profile.runningVideoPosteriorUrl}
                        label="Posterior View (Back)"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </TooltipProvider>
  );
}
