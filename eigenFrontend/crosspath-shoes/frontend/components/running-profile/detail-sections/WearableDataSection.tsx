"use client";

import React from "react";
import { RunningProfile } from "@/lib/types";
import { Watch, Gauge, Clock, MoveVertical, Info } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface WearableDataSectionProps {
  profile: RunningProfile;
}

const DataItem: React.FC<{ label: string; value: string | number | null | undefined; unit?: string; icon?: React.ElementType; explanation?: string; }> = ({ label, value, unit, icon: Icon, explanation }) => {
  return (
    <div className="flex items-start space-x-2 p-2 bg-background hover:bg-muted/50 rounded-md transition-colors duration-150 min-h-[56px]">
      {Icon && <Icon className={`h-4 w-4 mt-0.5 text-primary flex-shrink-0`} />}
      <div className="flex-grow">
        <div className="flex items-center space-x-1">
          <p className="text-xs font-medium text-muted-foreground">{label}</p>
          {explanation && (
            <Tooltip delayDuration={100}>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-muted-foreground hover:text-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs">
                <p>{explanation}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <p className={`text-sm font-semibold text-foreground`}>
          {value ? `${value}${unit || ''}` : "N/A"}
        </p>
      </div>
    </div>
  );
};

export function WearableDataSection({ profile }: WearableDataSectionProps) {
  const hasWearableData = 
    profile.runningPower !== null ||
    profile.groundContactTime !== null ||
    profile.verticalOscillationWearable !== null;

  return (
    <TooltipProvider>
      <Accordion type="single" collapsible defaultValue="wearable-data" className="w-full">
        <AccordionItem value="wearable-data" className="border-none">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 bg-card hover:bg-muted/50 rounded-lg shadow-sm transition-colors duration-150 group hover:no-underline focus-visible:no-underline">
            <div className="flex items-center space-x-2.5">
              <Watch className="h-5 w-5 text-primary" />
              <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors duration-150 no-underline group-hover:no-underline">
                Wearable Data Insights
              </h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-0 px-0.5 pb-0.5">
            <div className="border-t border-border dark:border-zinc-700/70 mt-1.5 mb-0.5"></div>
            <Card className="bg-transparent border-none shadow-none">
              <CardContent className="p-2 space-y-2">
              {!hasWearableData ? (
                  <p className="text-xs text-muted-foreground text-center py-3">No specific wearable metric data recorded for this profile.</p>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                    {profile.runningPower !== null && typeof profile.runningPower !== 'undefined' && (
                      <DataItem 
                        label="Running Power" 
                        value={profile.runningPower} 
                        unit=" Watts" 
                        icon={Gauge} 
                        explanation="Measures the intensity of your run in watts; higher power often indicates more effort." 
                      />
                    )}
                    {profile.groundContactTime !== null && typeof profile.groundContactTime !== 'undefined' && (
                      <DataItem 
                        label="Ground Contact Time" 
                        value={profile.groundContactTime} 
                        unit=" ms" 
                        icon={Clock} 
                        explanation="Duration foot is on the ground each step (ms); shorter times can indicate efficiency." 
                      />
                    )}
                    {profile.verticalOscillationWearable !== null && typeof profile.verticalOscillationWearable !== 'undefined' && (
                      <DataItem 
                        label="Vertical Oscillation" 
                        value={profile.verticalOscillationWearable} 
                        unit=" cm" 
                        icon={MoveVertical} 
                        explanation="Amount of 'bounce' in your stride (cm); lower values often suggest efficient form." 
                      />
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </TooltipProvider>
  );
}
