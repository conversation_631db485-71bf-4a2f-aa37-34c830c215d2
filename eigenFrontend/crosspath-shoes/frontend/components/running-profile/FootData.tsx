import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Footprints, ImageIcon } from "lucide-react";
import { RunningProfile } from "@/lib/types";
import Image from "next/image";

interface FootDataProps {
  profile: RunningProfile;
}

export function FootData({ profile }: FootDataProps) {
  const renderDetailItem = (
    label: string,
    value: string | number | null | undefined
  ) => {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    return (
      <div className="py-2">
        <div className="flex justify-between">
          <span className="text-sm text-zinc-500 dark:text-zinc-400">
            {label}
          </span>
          <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
            {value}
          </span>
        </div>
      </div>
    );
  };

  const renderFootImage = (url: string | null, label: string) => {
    if (!url) return null;

    // Check if the URL is valid
    const isValidUrl = url.startsWith("http") || url.startsWith("/");

    return (
      <div className="flex flex-col items-center gap-2 p-2">
        <div className="relative w-full h-40 rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700">
          {isValidUrl ? (
            <Image src={url} alt={label} fill className="object-cover" />
          ) : (
            <div className="flex items-center justify-center h-full w-full bg-zinc-100 dark:bg-zinc-800">
              <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
            </div>
          )}
        </div>
        <span className="text-xs text-zinc-500 dark:text-zinc-400">
          {label}
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
          <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
            <Footprints className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
            Foot Measurements
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <div className="grid sm:grid-cols-2 gap-x-6 divide-y sm:divide-y-0 divide-zinc-100 dark:divide-zinc-700/50">
            <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 sm:pr-3 sm:border-r border-zinc-100 dark:border-zinc-700/50">
              {renderDetailItem(
                "Heel-to-Toe Length (Left)",
                profile.heelToToeLengthLeft
                  ? `${profile.heelToToeLengthLeft} mm`
                  : null
              )}
              {renderDetailItem(
                "Heel-to-Toe Length (Right)",
                profile.heelToToeLengthRight
                  ? `${profile.heelToToeLengthRight} mm`
                  : null
              )}
              {renderDetailItem(
                "Forefoot Width (Left)",
                profile.forefootWidthLeft
                  ? `${profile.forefootWidthLeft} mm`
                  : null
              )}
              {renderDetailItem(
                "Forefoot Width (Right)",
                profile.forefootWidthRight
                  ? `${profile.forefootWidthRight} mm`
                  : null
              )}
            </div>
            <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 pt-3 sm:pt-0 sm:pl-3">
              {renderDetailItem(
                "Medial Arch Length (Left)",
                profile.medialArchLengthLeft
                  ? `${profile.medialArchLengthLeft} mm`
                  : null
              )}
              {renderDetailItem(
                "Medial Arch Length (Right)",
                profile.medialArchLengthRight
                  ? `${profile.medialArchLengthRight} mm`
                  : null
              )}
              {renderDetailItem(
                "Medial Arch Height (Left)",
                profile.medialArchHeightLeft
                  ? `${profile.medialArchHeightLeft} mm`
                  : null
              )}
              {renderDetailItem(
                "Medial Arch Height (Right)",
                profile.medialArchHeightRight
                  ? `${profile.medialArchHeightRight} mm`
                  : null
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Foot Images */}
      {(profile.footImageTopLeftUrl ||
        profile.footImageTopRightUrl ||
        profile.footImageMedialLeftUrl ||
        profile.footImageMedialRightUrl) && (
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
            <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
              <Footprints className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
              Foot Images
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-3">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {renderFootImage(profile.footImageTopLeftUrl, "Top View - Left")}
              {renderFootImage(
                profile.footImageTopRightUrl,
                "Top View - Right"
              )}
              {renderFootImage(
                profile.footImageMedialLeftUrl,
                "Medial View - Left"
              )}
              {renderFootImage(
                profile.footImageMedialRightUrl,
                "Medial View - Right"
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
