import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BadgeCheck, ChevronLeft, Edit, CalendarDays, UserCircle, Settings } from "lucide-react";
import { format } from "date-fns";

interface ProfileHeaderProps {
  id: string;
  name: string;
  isDefault: boolean;
  createdAt: string | Date;
}

export function ProfileHeader({
  id,
  name,
  isDefault,
  createdAt,
}: ProfileHeaderProps) {
  const router = useRouter();

  return (
    <div className="mb-6 sm:mb-8">
      <div className="mb-4 sm:mb-5">
        <Link href="/running-profiles" className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors group">
          <ChevronLeft className="h-4 w-4 mr-1.5 group-hover:-translate-x-0.5 transition-transform duration-150" />
          All Profiles
        </Link>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 bg-card border border-border shadow-lg rounded-xl p-5 sm:p-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-primary/10 text-primary">
            <UserCircle className="h-7 w-7 sm:h-8 sm:h-8" />
          </div>
          <div>
            <div className="flex items-center gap-2.5">
              <h1 className="text-2xl sm:text-3xl font-semibold text-foreground">
                {name}
              </h1>
              {isDefault && (
                <Badge variant="default" className="bg-primary/10 hover:bg-primary/20 text-primary border-primary/20 text-xs">
                  <BadgeCheck className="mr-1.5 h-3.5 w-3.5" />
                  Default
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1 flex items-center">
              <CalendarDays className="h-3.5 w-3.5 mr-1.5 opacity-70" />
              Created: {format(new Date(createdAt), "dd MMM, yyyy")}
            </p>
          </div>
        </div>
        <div className="flex-shrink-0">
          <Button
            variant="outline"
            onClick={() => router.push(`/running-profiles/${id}/edit`)}
            size="sm"
            className="border-primary/50 text-primary hover:bg-primary/10 hover:text-primary group transition-all duration-150 transform hover:scale-[1.02]"
          >
            <Settings className="h-4 w-4 mr-2 group-hover:rotate-45 transition-transform duration-200" />
            Manage Profile
          </Button>
        </div>
      </div>
    </div>
  );
}
