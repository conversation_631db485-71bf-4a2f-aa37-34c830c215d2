import React from "react";
import { RunningProfile } from "@/lib/types";
import { format } from "date-fns";
import {
  RunnerInfoSection,
  FootMeasurementsSection,
  WearableDataSection,
  VideoAnalysisSection,
  RecommendationsSection,
} from "./detail-sections";

interface ProfileDetailViewProps {
  profile: RunningProfile;
}

export function ProfileDetailView({ profile }: ProfileDetailViewProps) {
  return (
    <div className="space-y-2 w-full max-w-full overflow-hidden">
      {/* Main sections using the new components */}
      <RunnerInfoSection profile={profile} />
      <FootMeasurementsSection profile={profile} />
      <WearableDataSection profile={profile} />
      <VideoAnalysisSection profile={profile} />
      <RecommendationsSection
        profile={profile}
        recommendation={profile.recommendation}
      />

      {/* Profile Metadata */}
      <div className="text-xs text-zinc-500 dark:text-zinc-400 mt-8 pt-4 border-t border-zinc-200 dark:border-zinc-700">
        <p>
          Profile created on{" "}
          {format(new Date(profile.createdAt), "MMMM d, yyyy")}
        </p>
        <p>
          Last updated on {format(new Date(profile.updatedAt), "MMMM d, yyyy")}
        </p>
      </div>
    </div>
  );
}
