"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Brain, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface AnalyzeVideoButtonProps {
  profileId: string;
  videoUrl: string | null;
  videoType?: "sagittal" | "posterior";
  hasAnalysisData?: boolean;
  onAnalysisComplete?: () => void;
  className?: string;
}

export function AnalyzeVideoButton({
  profileId,
  videoUrl,
  videoType = "sagittal",
  hasAnalysisData = false,
  onAnalysisComplete,
  className = "",
}: AnalyzeVideoButtonProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(hasAnalysisData);
  const router = useRouter();

  const startAnalysis = async () => {
    if (!videoUrl) {
      toast.error("No video available for analysis");
      return;
    }

    setIsAnalyzing(true);

    try {
      const response = await fetch("/api/analyze-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          profileId,
          videoUrl,
          videoType,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Analysis failed");
      }

      if (data.success) {
        setAnalysisComplete(true);
        toast.success("Video analysis completed successfully!");
        
        // Call the completion callback if provided
        if (onAnalysisComplete) {
          onAnalysisComplete();
        }
        
        // Refresh the page to show updated data
        router.refresh();
      } else {
        throw new Error(data.error || "Analysis failed");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Analysis failed";
      toast.error(errorMessage);
      console.error("Video analysis error:", error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Don't show button if no video is available
  if (!videoUrl) {
    return null;
  }

  // Show completion status if analysis is done
  if (analysisComplete && !isAnalyzing) {
    return (
      <Button
        variant="outline"
        disabled
        className={`flex items-center gap-2 ${className}`}
      >
        <CheckCircle className="h-4 w-4 text-green-500" />
        Analysis Complete
      </Button>
    );
  }

  // Show analyzing status
  if (isAnalyzing) {
    return (
      <Button
        variant="outline"
        disabled
        className={`flex items-center gap-2 ${className}`}
      >
        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
        Analyzing...
      </Button>
    );
  }

  // Show analyze button
  return (
    <Button
      onClick={startAnalysis}
      className={`flex items-center gap-2 ${className}`}
    >
      <Brain className="h-4 w-4" />
      Analyze Video
    </Button>
  );
}
