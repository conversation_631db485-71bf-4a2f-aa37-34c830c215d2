import { createClient } from "@/utils/supabase/server";

// Get the current user from the server
export async function getCurrentUser() {
  try {
    // Create the Supabase client
    const supabase = await createClient();

    // Check if the client was created successfully
    if (!supabase || !supabase.auth) {
      console.error(
        "Failed to initialize Supabase client or auth is not available"
      );
      return null;
    }

    // Get the user directly
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      console.error("Error getting user:", error);
      return null;
    }

    if (!data || !data.user) {
      return null;
    }

    return data.user;
  } catch (error) {
    console.error("Error in getCurrentUser:", error);
    return null;
  }
}

// Get a user's profile data
export async function getUserProfile(userId: string) {
  try {
    // Create the Supabase client
    const supabase = await createClient();

    // Check if the client was created successfully
    if (!supabase) {
      console.error("Failed to initialize Supabase client");
      return null;
    }

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error in getUserProfile:", error);
    return null;
  }
}
