import { Shoe, Order, RunningMetric } from "./types";

export const mockShoes: Shoe[] = [
  {
    id: "shoe-1",
    name: "eigenVECTOR",
    model: "shoe1",
    status: "active",
    totalMileage: 423,
    maxMileage: 500,
    purchaseDate: "2024-01-15",
    customizations: {
      color: "#2563eb",
      size: 42,
      width: "standard",
      cushioning: 5,
      support: 3,
      breathability: true,
      style: "athletic",
    },
    size: {
      eu: 42,
      us: 9,
      uk: 6.5,
    },
  },
  {
    id: "shoe-2",
    name: "eigenFREQUENCY",
    model: "shoe2",
    status: "ordered",
    totalMileage: 0,
    maxMileage: 600,
    purchaseDate: "2024-06-15",
    customizations: {
      color: "#16a34a",
      size: 42,
      width: "wide",
      cushioning: 5,
      support: 4,
      breathability: true,
      style: "casual",
    },
    size: {
      eu: 42,
      us: 9,
      uk: 6.5,
    },
  },
  {
    id: "shoe-3",
    name: "eigenVALUE",
    model: "shoe3",
    status: "retired",
    totalMileage: 512,
    maxMileage: 500,
    purchaseDate: "2023-08-01",
    customizations: {
      color: "#dc2626",
      size: 42,
      width: "standard",
      cushioning: 5,
      support: 3,
      breathability: true,
      style: "performance",
    },
    size: {
      eu: 42,
      us: 9,
      uk: 6.5,
    },
  },
];

export const mockOrders: Order[] = [
  {
    id: "order1",
    shoeId: "shoe3",
    status: "in_production",
    orderDate: "2024-03-01",
    estimatedDelivery: "2024-06-15",
    price: 199.99,
    shoeName: "CrossPath Urban Light",
    previewImage: "/shoes/urban-light-preview.jpg",
  },
  {
    id: "order2",
    shoeId: "shoe1",
    status: "delivered",
    orderDate: "2024-01-15",
    estimatedDelivery: "2024-02-15",
    trackingNumber: "CP123456789",
    price: 249.99,
    shoeName: "CrossPath Runner Pro",
    previewImage: "/shoes/runner-pro-preview.jpg",
  },
];

// Generate 30 days of running data
export const mockRunningMetrics: RunningMetric[] = Array.from(
  { length: 30 },
  (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);

    // Generate some realistic variations in the metrics
    const baseDistance = 8 + Math.random() * 4; // 8-12km
    const basePace = 5 + Math.random(); // 5-6 min/km
    const duration = baseDistance * basePace;

    return {
      date: date.toISOString().split("T")[0],
      distance: parseFloat(baseDistance.toFixed(1)),
      duration: parseFloat(duration.toFixed(1)),
      pace: parseFloat(basePace.toFixed(2)),
      cadence: Math.round(170 + Math.random() * 10), // 170-180 spm
      elevation: Math.round(50 + Math.random() * 100), // 50-150m
      shoeId: "shoe1", // Most recent runs with the active shoe
    };
  }
);

export const userSettings = {
  personalInfo: {
    name: "Alex Runner",
    email: "<EMAIL>",
    preferredDistance: "10k",
    weeklyGoal: 40, // km
  },
  notifications: {
    orderUpdates: true,
    maintenanceReminders: true,
    achievementAlerts: true,
    weeklyReports: true,
  },
  units: {
    distance: "kilometers",
    pace: "min/km",
    weight: "kg",
  },
  privacySettings: {
    shareRunningData: true,
    publicProfile: false,
    allowFriendRequests: true,
  },
};
