export interface User {
  id: string;
  name: string | null;
  email: string;
  emailVerified: Date | null;
  image: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null; // Soft delete timestamp
  runningProfiles?: RunningProfile[];
}

export interface Shoe {
  id: string;
  name: string;
  model: string;
  purchaseDate: string;
  totalMileage: number;
  maxMileage: number;
  status: "active" | "retired" | "ordered";
  customizations?: {
    cushioning: number;
    width: "narrow" | "standard" | "wide";
    size: number;
    support: number;
    breathability: boolean;
    style: "athletic" | "casual" | "performance";
    color: string;
  };
  size: {
    eu: number;
    us: number;
    uk: number;
  };
}

export interface Order {
  id: string;
  shoeId: string;
  status: "pending" | "in_production" | "shipped" | "delivered";
  orderDate: string;
  estimatedDelivery: string;
  trackingNumber?: string;
  price: number;
  shoeName: string;
  previewImage?: string;
}

export interface RunningMetric {
  date: string;
  distance: number;
  duration: number;
  pace: number;
  cadence: number;
  elevation: number;
  shoeId: string;
}

export interface ShoeModel {
  id: string;
  brand: string;
  name: string;
  fullName: string;
  modelYear: number;
  category: string;
  price: number;
  imageURLs: string[];

  // Basic fields
  description: string;
  pros: string[];
  cons: string[];
  rating?: number;
  numberOfReviews: number;
  purchaseUrl?: string; // URL to purchase the shoe

  // Technical specifications
  heelStackHeight?: number; // Heel stack height in mm
  drop?: number; // Heel to toe drop in mm
  midsoleWidth?: string; // Midsole width (general)
  stability?: string; // Neutral, Stability, Motion Control
  midsoleSoftness?: number; // Midsole softness scale 1-10
  forefootFlexibility?: number; // Scale 1-10
  midsoleSoftnessInCold?: number; // Midsole softness in cold scale 1-10
  toeboxWidth?: string; // Toebox width
  toeboxHeight?: string; // Toebox height
  type?: string; // Brand specification (e.g., Daily Trainer, Racing)
  midsoleFormCategory?: string; // EVA, TPU, PEBA, etc.
  archSupport?: string; // Brand specification
  archType?: string; // Low, Medium, High
  pronation?: string; // Neutral, Overpronation, Underpronation
  tongueThickness?: string; // Thin, Medium, Thick
  torsionalRigidity?: number; // Torsional rigidity scale 1-10
  shoeWeightG?: number; // Weight in grams
  rockerGeometry?: boolean; // Has rocker geometry
  outsoleThickness?: number; // Outsole thickness in mm
  terrain?: string; // Road, Trail, Track, etc.
  medialLateralPosting?: boolean; // Has medial/lateral posting
  tongueType?: string; // Gusseted, Standard, etc.

  // New fields
  insoleThickness?: number; // Insole thickness in mm
  size?: number; // Shoe size
  widthFit?: string; // Width / Fit
  heelCounterStiffness?: number; // Heel counter stiffness
  midsoleWidthForefoot?: string; // Midsole width - forefoot
  midsoleWidthHeel?: string; // Midsole width - heel
  flexibilityStiffness?: number; // Flexibility / Stiffness
  stiffnessInCold?: number; // Stiffness in cold
  tonguePadding?: string; // Tongue padding

  // Note: recommendations field is not included in this interface
  // even though it exists in the Prisma schema for relation validation
}

export interface ShoeRecommendation {
  id: string;
  createdAt: string;
  updatedAt: string;

  // Running profile relation
  runningProfileId: string;

  // Top 5 shoe recommendations
  shoeModel1Id?: string;
  shoeModel1?: ShoeModel;
  explanation1?: string;

  shoeModel2Id?: string;
  shoeModel2?: ShoeModel;
  explanation2?: string;

  shoeModel3Id?: string;
  shoeModel3?: ShoeModel;
  explanation3?: string;

  shoeModel4Id?: string;
  shoeModel4?: ShoeModel;
  explanation4?: string;

  shoeModel5Id?: string;
  shoeModel5?: ShoeModel;
  explanation5?: string;

  // Overall recommendation summary
  overallExplanation?: string;
}

// Alias for backward compatibility
export type RecommendationWithShoe = ShoeRecommendation;

export interface RunningProfile {
  // System Metadata
  id: string; // System-generated unique identifier
  name: string; // Profile name (User Input)
  createdAt: string; // Creation timestamp
  updatedAt: string; // Last update timestamp
  isDefault: boolean; // Indicates default profile
  isCompleted?: boolean; // Indicates if profile creation is completed
  userId: string; // User identifier

  // Recommendation
  recommendation?: ShoeRecommendation;

  // Runner Profile Data
  age: number | null; // Runner's age (years)
  heightCm: number | null; // Runner's height in cm
  weightKg: number | null; // Runner's weight in kg
  gender: string | null; // Gender (Male/Female)
  climate: string | null; // Typical climate (Cold/Temperate/Hot/Variable)
  terrain: string | null; // Most frequently run surface (Road/Trails/Treadmill/Grass)
  runningGoal: string | null; // Training, Racing, Recreational
  previousInjuries: string | null; // Previous injuries (categorical string from fixed options)
  previousInjuriesSeverity: string | null; // Severity of previous injuries
  averageWeeklyKm: string | null; // Weekly running distance (categorical)
  averagePaceEasyLong: string | null; // Avg. running pace for easy/long runs
  averageCadence: number | null; // Avg. running cadence (steps/min)

  // Wearable Data
  runningPower: number | null; // Power output during running (Watts)
  groundContactTime: number | null; // Ground contact time (milliseconds)
  verticalOscillationWearable: number | null; // Vertical oscillation (cm)

  // Foot Images (URLs)
  footImageTopLeftUrl: string | null; // URL of top-down image of left foot
  footImageTopRightUrl: string | null; // URL of top-down image of right foot
  footImageMedialLeftUrl: string | null; // URL of medial side image of left foot
  footImageMedialRightUrl: string | null; // URL of medial side image of right foot

  // Foot Measurements (Left/Right specific)
  heelToToeLengthLeft: number | null; // Heel-to-toe length from images - left foot (mm)
  heelToToeLengthRight: number | null; // Heel-to-toe length from images - right foot (mm)
  forefootWidthLeft: number | null; // Forefoot width from images - left foot (mm)
  forefootWidthRight: number | null; // Forefoot width from images - right foot (mm)
  medialArchLengthLeft: number | null; // Medial arch length from images - left foot (mm)
  medialArchLengthRight: number | null; // Medial arch length from images - right foot (mm)
  medialArchHeightLeft: number | null; // Medial arch height from images - left foot (mm)
  medialArchHeightRight: number | null; // Medial arch height from images - right foot (mm)

  // Legacy fields have been removed

  // Running Videos
  runningVideoPosteriorUrl: string | null; // URL for posterior view running video
  runningVideoSagittalUrl: string | null; // URL for sagittal (side) view running video
  runningVideoPosteriorTimestamp: number | null; // Timestamp for posterior video clip (s)
  runningVideoSagittalTimestamp: number | null; // Timestamp for sagittal video clip (s)

  // Running Video Analysis
  pelvicDrop: number | null; // Pelvic drop direction
  kneeDrift: number | null; // Knee drift (Valgus / Varus)
  footDrift: number | null; // Foot drift/Crossover gait
  heelWhip: number | null; // Medial/Lateral Whip
  posteriorStabilityScore: number | null; // Stability score from video
  overstride: number | null; // Overstride detection
  ankleDorsiflexion: number | null; // Foot strike type (heelstrike/midfoot/forefoot)
  anklePlantarflexion: number | null; // Measures propulsive power and push-off mechanics
  verticalOscillationVideo: number | null; // Vertical oscillation (cm)
  trunkLean: number | null; // Trunk lean angle (degrees)
  kneeFlexionLoading: number | null; // Knee flexion angle during loading (degrees)

  // AI Analysis Results
  gaitAnalysisSummary: string | null; // AI-generated summary of gait analysis
  criticalObservationsJson: string | null; // JSON string of critical observations
}
