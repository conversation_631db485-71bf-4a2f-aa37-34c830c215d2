/**
 * Uploads a file to the server and returns the URLs
 *
 * @param file - The file to upload
 * @param profileId - The ID of the running profile
 * @param fieldToUpdate - The field to update in the running profile
 * @returns Object containing the signed URL for immediate use and storage path for reference
 */
export async function uploadFile(
  file: File,
  profileId: string,
  fieldToUpdate: string
): Promise<{ fileUrl?: string; storagePath?: string; error?: string }> {
  try {
    // Create form data
    const formData = new FormData();
    formData.append("file", file);
    formData.append("profileId", profileId);
    formData.append("fieldToUpdate", fieldToUpdate);

    // Send request to API
    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    // Parse response
    const data = await response.json();

    if (!response.ok) {
      return { error: data.error || "Failed to upload file" };
    }

    return {
      fileUrl: data.fileUrl, // Signed URL for immediate use
      storagePath: data.storagePath, // Storage path for reference
    };
  } catch (error) {
    console.error("Error uploading file:", error);
    return { error: "An unexpected error occurred during upload" };
  }
}
