import {
  createServiceClient,
  BUCKET_IMAGES,
  BUCKET_VIDEOS,
} from "./supabase-storage";

// Simple type definitions
export type StoragePath = {
  bucket: string;
  filePath: string;
};

export type MediaType = "image" | "video";

// Global URL cache to avoid repeated requests
type UrlCache = {
  [key: string]: {
    url: string;
    expiresAt: number;
  };
};

// Cache with 1-hour expiration by default
const URL_CACHE: UrlCache = {};

/**
 * Parses a storage path string into bucket and file path components
 * Format: "bucket:filePath"
 */
export function parseStoragePath(storagePath: string): StoragePath | null {
  if (!storagePath) return null;

  const parts = storagePath.split(":");
  if (parts.length === 2) {
    const [bucket, filePath] = parts;
    if (bucket && filePath) {
      return { bucket, filePath };
    }
  }

  return null;
}

/**
 * Formats a bucket and file path into a storage path string
 */
export function formatStoragePath(bucket: string, filePath: string): string {
  return `${bucket}:${filePath}`;
}

/**
 * Gets the appropriate bucket for a media type
 */
export function getBucketForMediaType(mediaType: MediaType): string {
  return mediaType === "image" ? BUCKET_IMAGES : BUCKET_VIDEOS;
}

/**
 * A single, simplified helper function to get a media URL from any source
 * - Handles already signed URLs
 * - Handles storage paths (bucket:filePath)
 * - Uses caching to avoid repeated requests
 * - Works both client-side and server-side
 *
 * @param path The media path (storage path or URL)
 * @param isServer Whether this is being called from the server
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns The media URL (signed if needed)
 */
export async function getMediaUrl(
  path: string | StoragePath | null | undefined,
  isServer: boolean = false,
  expiresIn: number = 3600
): Promise<string | null> {
  // Handle null/undefined
  if (!path) return null;

  // Convert StoragePath object to string if needed
  const pathString =
    typeof path === "string"
      ? path
      : formatStoragePath(path.bucket, path.filePath);

  // If it's already a signed URL, return it directly
  if (pathString.includes("token=")) {
    return pathString;
  }

  // Check cache first (only for client-side)
  if (!isServer) {
    const now = Date.now();
    const cached = URL_CACHE[pathString];
    if (cached && cached.expiresAt > now) {
      return cached.url;
    }
  }

  try {
    // Parse the storage path
    const parsedPath = parseStoragePath(pathString);
    if (!parsedPath) {
      console.error("Invalid storage path format:", pathString);
      return null;
    }

    // Generate signed URL
    let signedUrl: string | null = null;

    if (isServer) {
      // Server-side: use Supabase service client directly
      try {
        const supabase = createServiceClient();
        console.log(
          `[DEBUG] Creating signed URL for ${parsedPath.bucket}:${parsedPath.filePath}`
        );

        const { data, error } = await supabase.storage
          .from(parsedPath.bucket)
          .createSignedUrl(parsedPath.filePath, expiresIn);

        if (error) {
          console.error("Error creating signed URL:", error);
          if (error.message.includes("jwt malformed")) {
            console.error(
              "JWT malformed error detected. This is likely due to an invalid SUPABASE_SERVICE_ROLE_KEY."
            );
          }
          return null;
        }

        if (!data || !data.signedUrl) {
          console.error("No signed URL returned from Supabase");
          return null;
        }

        console.log(
          `[DEBUG] Successfully created signed URL (expires in ${expiresIn}s)`
        );
        signedUrl = data.signedUrl;
      } catch (error) {
        console.error("Exception in server-side signed URL generation:", error);
        return null;
      }
    } else {
      // Client-side: use API endpoint
      const response = await fetch("/api/storage/signed-url", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          storagePath: pathString,
          expiresIn,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error creating signed URL:", errorData);
        return null;
      }

      const data = await response.json();
      signedUrl = data.signedUrl || null;

      // Cache the URL (client-side only)
      if (signedUrl) {
        URL_CACHE[pathString] = {
          url: signedUrl,
          expiresAt: Date.now() + expiresIn * 1000 * 0.9, // Expire slightly earlier
        };
      }
    }

    return signedUrl;
  } catch (error) {
    console.error("Error generating media URL:", error);
    return null;
  }
}

/**
 * Server-side helper function (alias for getMediaUrl with isServer=true)
 */
export async function getMediaUrlServer(
  path: string | StoragePath | null | undefined,
  expiresIn: number = 3600
): Promise<string | null> {
  try {
    // Log the environment variables (without revealing full values)
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
    const hasServiceKey = !!process.env.SUPABASE_SERVICE_ROLE_KEY;
    console.log(`[DEBUG] Using Supabase URL: ${supabaseUrl}`);
    console.log(`[DEBUG] Has service key: ${hasServiceKey}`);

    return await getMediaUrl(path, true, expiresIn);
  } catch (error) {
    console.error("Error in getMediaUrlServer:", error);
    // Return null instead of throwing to prevent breaking the UI
    return null;
  }
}
