-- AlterTable
-- Change field types for existing columns
ALTER TABLE "RunningProfile" ALTER COLUMN "previousInjuries" DROP DEFAULT;
ALTER TABLE "RunningProfile" ALTER COLUMN "previousInjuries" TYPE TEXT;

-- Add new fields
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "previousInjuriesSeverity" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "runningVideoPosteriorTimestamp" DOUBLE PRECISION;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "runningVideoSagittalTimestamp" DOUBLE PRECISION;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "anklePlantarflexion" DOUBLE PRECISION;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "footDrift" DOUBLE PRECISION;

-- Add image URL fields if they don't exist
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "footImageTopLeftUrl" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "footImageTopRightUrl" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "footImageMedialLeftUrl" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "footImageMedialRightUrl" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "runningVideoPosteriorUrl" TEXT;
ALTER TABLE "RunningProfile" ADD COLUMN IF NOT EXISTS "runningVideoSagittalUrl" TEXT;

-- Change field types for analysis fields
ALTER TABLE "RunningProfile" 
  ALTER COLUMN "pelvicDrop" TYPE DOUBLE PRECISION USING CASE WHEN "pelvicDrop" IS NULL THEN NULL ELSE "pelvicDrop"::DOUBLE PRECISION END,
  ALTER COLUMN "kneeDrift" TYPE DOUBLE PRECISION USING CASE WHEN "kneeDrift" IS NULL THEN NULL ELSE "kneeDrift"::DOUBLE PRECISION END,
  ALTER COLUMN "heelWhip" TYPE DOUBLE PRECISION USING CASE WHEN "heelWhip" IS NULL THEN NULL ELSE "heelWhip"::DOUBLE PRECISION END,
  ALTER COLUMN "overstride" TYPE DOUBLE PRECISION USING CASE WHEN "overstride" IS NULL THEN NULL ELSE "overstride"::DOUBLE PRECISION END,
  ALTER COLUMN "ankleDorsiflexion" TYPE DOUBLE PRECISION USING CASE WHEN "ankleDorsiflexion" IS NULL THEN NULL ELSE "ankleDorsiflexion"::DOUBLE PRECISION END;
