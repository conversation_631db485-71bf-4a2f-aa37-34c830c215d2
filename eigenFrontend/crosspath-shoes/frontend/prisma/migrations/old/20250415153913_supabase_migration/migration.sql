/*
  Warnings:

  - You are about to drop the column `footDriftCrossover` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `forefootWidthHeges` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `heelToToeLengthHeges` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `instepHeightHeges` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `medialArchHeightHeges` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `medialArchLengthHeges` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to drop the column `surfaceCurvatureMap` on the `RunningProfile` table. All the data in the column will be lost.
  - You are about to alter the column `heightCm` on the `RunningProfile` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to alter the column `weightKg` on the `RunningProfile` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to alter the column `runningPower` on the `RunningProfile` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to alter the column `groundContactTime` on the `RunningProfile` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Integer`.
  - You are about to drop the column `password` on the `User` table. All the data in the column will be lost.
  - You are about to drop the `Account` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Session` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `VerificationToken` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `updatedAt` to the `User` table without a default value. This is not possible if the table is not empty.
  - Made the column `email` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "Account" DROP CONSTRAINT "Account_userId_fkey";

-- DropForeignKey
ALTER TABLE "Session" DROP CONSTRAINT "Session_userId_fkey";

-- AlterTable
ALTER TABLE "RunningProfile" DROP COLUMN "footDriftCrossover",
DROP COLUMN "forefootWidthHeges",
DROP COLUMN "heelToToeLengthHeges",
DROP COLUMN "instepHeightHeges",
DROP COLUMN "medialArchHeightHeges",
DROP COLUMN "medialArchLengthHeges",
DROP COLUMN "surfaceCurvatureMap",
ALTER COLUMN "heightCm" SET DATA TYPE INTEGER,
ALTER COLUMN "weightKg" SET DATA TYPE INTEGER,
ALTER COLUMN "averageWeeklyKm" SET DATA TYPE TEXT,
ALTER COLUMN "runningPower" SET DATA TYPE INTEGER,
ALTER COLUMN "groundContactTime" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "password",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "email" SET NOT NULL;

-- DropTable
DROP TABLE "Account";

-- DropTable
DROP TABLE "Session";

-- DropTable
DROP TABLE "VerificationToken";
