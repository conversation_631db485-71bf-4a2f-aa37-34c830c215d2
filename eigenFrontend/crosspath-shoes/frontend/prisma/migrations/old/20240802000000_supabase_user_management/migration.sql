-- This migration sets up the Supabase user management integration

-- 1. Create the trigger function for handling new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public."User" (id, name, email, "createdAt", "updatedAt")
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    NEW.email,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create the trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 3. Optional: Handle User Deletion (if ON DELETE CASCADE is not used)
-- Note: This is not needed if you have ON DELETE CASCADE in your User table's foreign key constraint
CREATE OR REPLACE FUNCTION public.handle_deleted_user()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM public."User" WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_auth_user_deleted
AFTER DELETE ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_deleted_user();

-- 4. Ensure the User table has the correct foreign key constraint with ON DELETE CASCADE
-- This is a safety check - if the table already exists with the correct constraint, this will do nothing
DO $$
BEGIN
  -- Check if the User table exists
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'User') THEN
    -- Check if the foreign key constraint exists without ON DELETE CASCADE
    IF EXISTS (
      SELECT 1 FROM pg_constraint c
      JOIN pg_namespace n ON n.oid = c.connamespace
      WHERE c.contype = 'f' 
      AND n.nspname = 'public'
      AND c.conrelid = 'public."User"'::regclass
      AND NOT c.confdeltype = 'c'  -- 'c' is for CASCADE
    ) THEN
      -- Drop the existing foreign key constraint
      EXECUTE (
        SELECT 'ALTER TABLE public."User" DROP CONSTRAINT ' || conname
        FROM pg_constraint c
        JOIN pg_namespace n ON n.oid = c.connamespace
        WHERE c.contype = 'f' 
        AND n.nspname = 'public'
        AND c.conrelid = 'public."User"'::regclass
        LIMIT 1
      );
      
      -- Add the foreign key constraint with ON DELETE CASCADE
      ALTER TABLE public."User" 
      ADD CONSTRAINT user_id_fkey 
      FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
  END IF;
END $$;
