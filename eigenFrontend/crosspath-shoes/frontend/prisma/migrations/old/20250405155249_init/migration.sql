-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "password" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "RunningProfile" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "age" INTEGER,
    "heightCm" DOUBLE PRECISION,
    "weightKg" DOUBLE PRECISION,
    "gender" TEXT,
    "climate" TEXT,
    "terrain" TEXT,
    "previousInjuries" TEXT[],
    "averageWeeklyKm" DOUBLE PRECISION,
    "averagePaceEasyLong" TEXT,
    "averageCadence" INTEGER,
    "footScanTopPhoto" TEXT,
    "footScanLeftPhoto" TEXT,
    "footScanRightPhoto" TEXT,
    "footScanBackPhoto" TEXT,
    "heelToToeLengthImage" DOUBLE PRECISION,
    "forefootWidthImage" DOUBLE PRECISION,
    "medialArchLengthImage" DOUBLE PRECISION,
    "medialArchHeightImage" DOUBLE PRECISION,
    "runningVideoSide" TEXT,
    "runningVideoBack" TEXT,
    "pelvicDrop" TEXT,
    "kneeDrift" TEXT,
    "footDriftCrossover" TEXT,
    "heelWhip" TEXT,
    "posteriorStabilityScore" DOUBLE PRECISION,
    "overstride" TEXT,
    "ankleDorsiflexion" TEXT,
    "verticalOscillationVideo" DOUBLE PRECISION,
    "trunkLean" DOUBLE PRECISION,
    "kneeFlexionLoading" DOUBLE PRECISION,
    "surfaceCurvatureMap" TEXT,
    "heelToToeLengthHeges" DOUBLE PRECISION,
    "forefootWidthHeges" DOUBLE PRECISION,
    "medialArchLengthHeges" DOUBLE PRECISION,
    "medialArchHeightHeges" DOUBLE PRECISION,
    "instepHeightHeges" DOUBLE PRECISION,
    "runningPower" DOUBLE PRECISION,
    "groundContactTime" DOUBLE PRECISION,
    "verticalOscillationWearable" DOUBLE PRECISION,

    CONSTRAINT "RunningProfile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShoeModel" (
    "id" TEXT NOT NULL,
    "brand" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "modelYear" INTEGER NOT NULL,
    "category" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "image" TEXT NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "heelStackHeight" DOUBLE PRECISION,
    "forefootStackHeight" DOUBLE PRECISION,
    "drop" DOUBLE PRECISION,
    "weight" DOUBLE PRECISION,
    "cushioningLevel" TEXT NOT NULL,
    "cushioningSoftness" DOUBLE PRECISION,
    "stability" TEXT NOT NULL,
    "flexibility" TEXT NOT NULL,
    "durability" TEXT NOT NULL,
    "breathability" INTEGER,
    "bestFor" TEXT[],
    "terrainTypes" TEXT[],
    "archTypes" TEXT[],
    "pronationTypes" TEXT[],
    "description" TEXT NOT NULL,
    "pros" TEXT[],
    "cons" TEXT[],
    "rating" DOUBLE PRECISION,
    "numberOfReviews" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShoeModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Recommendation" (
    "id" TEXT NOT NULL,
    "matchScore" DOUBLE PRECISION NOT NULL,
    "matchReasons" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "runningProfileId" TEXT NOT NULL,
    "shoeModelId" TEXT NOT NULL,

    CONSTRAINT "Recommendation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "ShoeModel_brand_name_modelYear_key" ON "ShoeModel"("brand", "name", "modelYear");

-- CreateIndex
CREATE UNIQUE INDEX "Recommendation_runningProfileId_shoeModelId_key" ON "Recommendation"("runningProfileId", "shoeModelId");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RunningProfile" ADD CONSTRAINT "RunningProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_runningProfileId_fkey" FOREIGN KEY ("runningProfileId") REFERENCES "RunningProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_shoeModelId_fkey" FOREIGN KEY ("shoeModelId") REFERENCES "ShoeModel"("id") ON DELETE CASCADE ON UPDATE CASCADE;
