-- AlterTable for ShoeModel - Rename columns
ALTER TABLE "ShoeModel" 
  RENAME COLUMN "heelStackHeightMm" TO "heelStackHeight";

-- AlterTable for ShoeModel - Rename columns
ALTER TABLE "ShoeModel" 
  RENAME COLUMN "heelToToeDropMm" TO "drop";

-- AlterTable for ShoeModel - Rename columns
ALTER TABLE "ShoeModel" 
  RENAME COLUMN "softnessMidsole" TO "midsoleSoftness";

-- AlterTable for ShoeModel - Rename columns
ALTER TABLE "ShoeModel" 
  RENAME COLUMN "outsoleThicknessMm" TO "outsoleThickness";

-- AlterTable for ShoeModel - Add new columns
ALTER TABLE "ShoeModel"
  ADD COLUMN IF NOT EXISTS "insoleThickness" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "size" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "widthFit" TEXT,
  ADD COLUMN IF NOT EXISTS "toeboxHeight" TEXT,
  ADD COLUMN IF NOT EXISTS "heelCounterStiffness" INTEGER,
  ADD COLUMN IF NOT EXISTS "midsoleWidthForefoot" TEXT,
  ADD COLUMN IF NOT EXISTS "midsoleWidthHeel" TEXT,
  ADD COLUMN IF NOT EXISTS "flexibilityStiffness" INTEGER,
  ADD COLUMN IF NOT EXISTS "stiffnessInCold" INTEGER,
  ADD COLUMN IF NOT EXISTS "tonguePadding" TEXT;
