-- CreateTable if not exists
CREATE TABLE IF NOT EXISTS "ShoeModel" (
  "id" TEXT NOT NULL,
  "brand" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "fullName" TEXT NOT NULL,
  "modelYear" INTEGER NOT NULL,
  "category" TEXT NOT NULL,
  "price" DOUBLE PRECISION NOT NULL,
  "heelStackHeightMm" DOUBLE PRECISION,
  "forefootStackHeight" DOUBLE PRECISION,
  "description" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "ShoeModel_pkey" PRIMARY KEY ("id")
);

-- Check if the table exists and has the columns we want to drop
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'ShoeModel' AND column_name = 'image'
  ) THEN
    -- AlterTable for ShoeModel - Remove columns
    ALTER TABLE "ShoeModel"
      DROP COLUMN IF EXISTS "image",
      DROP COLUMN IF EXISTS "cushioningLevel",
      DROP COLUMN IF EXISTS "cushioningSoftness",
      DROP COLUMN IF EXISTS "durability",
      DROP COLUMN IF EXISTS "flexibility",
      DROP COLUMN IF EXISTS "breathability",
      DROP COLUMN IF EXISTS "drop",
      DROP COLUMN IF EXISTS "weight";
  END IF;
END $$;

-- AlterTable for ShoeModel - Add new columns
ALTER TABLE "ShoeModel"
  ADD COLUMN IF NOT EXISTS "imageURLs" TEXT[],
  ADD COLUMN IF NOT EXISTS "heelToToeDropMm" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "midsoleWidth" TEXT,
  ADD COLUMN IF NOT EXISTS "softnessMidsole" INTEGER,
  ADD COLUMN IF NOT EXISTS "forefootFlexibility" INTEGER,
  ADD COLUMN IF NOT EXISTS "midsoleSoftnessInCold" INTEGER,
  ADD COLUMN IF NOT EXISTS "toeboxWidth" TEXT,
  ADD COLUMN IF NOT EXISTS "midsoleFormCategory" TEXT,
  ADD COLUMN IF NOT EXISTS "archSupport" TEXT,
  ADD COLUMN IF NOT EXISTS "archType" TEXT,
  ADD COLUMN IF NOT EXISTS "pronation" TEXT,
  ADD COLUMN IF NOT EXISTS "tongueThickness" TEXT,
  ADD COLUMN IF NOT EXISTS "torsionalRigidity" INTEGER,
  ADD COLUMN IF NOT EXISTS "shoeWeightG" INTEGER,
  ADD COLUMN IF NOT EXISTS "rockerGeometry" BOOLEAN,
  ADD COLUMN IF NOT EXISTS "outsoleThicknessMm" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "terrain" TEXT,
  ADD COLUMN IF NOT EXISTS "medialLateralPosting" BOOLEAN,
  ADD COLUMN IF NOT EXISTS "tongueType" TEXT;

-- Check if the column exists before renaming
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'ShoeModel' AND column_name = 'heelStackHeight'
  ) THEN
    -- AlterTable for ShoeModel - Rename column
    ALTER TABLE "ShoeModel" RENAME COLUMN "heelStackHeight" TO "heelStackHeightMm";
  END IF;
END $$;

-- Add unique constraint for ShoeModel if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'ShoeModel_brand_name_modelYear_key'
  ) THEN
    ALTER TABLE "ShoeModel" ADD CONSTRAINT "ShoeModel_brand_name_modelYear_key" UNIQUE ("brand", "name", "modelYear");
  END IF;
END $$;

-- CreateTable if not exists
CREATE TABLE IF NOT EXISTS "Recommendation" (
  "id" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "runningProfileId" TEXT NOT NULL,
  "shoeModelId" TEXT NOT NULL,

  CONSTRAINT "Recommendation_pkey" PRIMARY KEY ("id")
);

-- Check if the table exists and has the columns we want to drop
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'matchScore'
  ) THEN
    -- AlterTable for Recommendation - Remove columns
    ALTER TABLE "Recommendation"
      DROP COLUMN IF EXISTS "matchScore",
      DROP COLUMN IF EXISTS "matchReasons";
  END IF;
END $$;

-- AlterTable for Recommendation - Add new columns
ALTER TABLE "Recommendation"
  ADD COLUMN IF NOT EXISTS "overallMatchScore" DOUBLE PRECISION NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS "rank" INTEGER NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ADD COLUMN IF NOT EXISTS "stabilityScore" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "cushioningScore" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "fitScore" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "performanceScore" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "terrainScore" DOUBLE PRECISION,
  ADD COLUMN IF NOT EXISTS "stabilityExplanation" TEXT,
  ADD COLUMN IF NOT EXISTS "cushioningExplanation" TEXT,
  ADD COLUMN IF NOT EXISTS "fitExplanation" TEXT,
  ADD COLUMN IF NOT EXISTS "performanceExplanation" TEXT,
  ADD COLUMN IF NOT EXISTS "terrainExplanation" TEXT,
  ADD COLUMN IF NOT EXISTS "overallExplanation" TEXT NOT NULL DEFAULT '',
  ADD COLUMN IF NOT EXISTS "keyStrengths" TEXT[],
  ADD COLUMN IF NOT EXISTS "keyWeaknesses" TEXT[];

-- Update default values after migration
UPDATE "Recommendation" SET "overallExplanation" = 'No explanation available' WHERE "overallExplanation" = '';

-- Add unique constraint if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'Recommendation_runningProfileId_shoeModelId_key'
  ) THEN
    ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_runningProfileId_shoeModelId_key" UNIQUE ("runningProfileId", "shoeModelId");
  END IF;
END $$;

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'Recommendation_runningProfileId_fkey'
  ) THEN
    ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_runningProfileId_fkey"
      FOREIGN KEY ("runningProfileId") REFERENCES "RunningProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'Recommendation_shoeModelId_fkey'
  ) THEN
    ALTER TABLE "Recommendation" ADD CONSTRAINT "Recommendation_shoeModelId_fkey"
      FOREIGN KEY ("shoeModelId") REFERENCES "ShoeModel"("id") ON DELETE CASCADE ON UPDATE CASCADE;
  END IF;
END $$;
