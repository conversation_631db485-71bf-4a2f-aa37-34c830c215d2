-- Update the trigger function for handling new users to check for soft-deleted users with the same email
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  soft_deleted_user_id UUID;
BEGIN
  -- Check if there's a soft-deleted user with the same email
  SELECT id INTO soft_deleted_user_id
  FROM public."User"
  WHERE email = NEW.email AND "deletedAt" IS NOT NULL
  LIMIT 1;
  
  IF soft_deleted_user_id IS NOT NULL THEN
    -- Update the soft-deleted user with the new auth ID
    UPDATE public."User"
    SET id = NEW.id,
        name = COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
        "emailVerified" = CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN NEW.email_confirmed_at ELSE NULL END,
        "deletedAt" = NULL,
        "updatedAt" = NOW()
    WHERE id = soft_deleted_user_id;
  ELSE
    -- Insert a new user record if no soft-deleted user was found
    INSERT INTO public."User" (id, name, email, "emailVerified", "createdAt", "updatedAt")
    VALUES (
      NEW.id,
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      NEW.email,
      CASE WHEN NEW.email_confirmed_at IS NOT NULL THEN NEW.email_confirmed_at ELSE NULL END,
      NOW(),
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
