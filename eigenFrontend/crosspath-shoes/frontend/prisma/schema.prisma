// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DIRECT_URL")
}

// User model - linked to Supabase Auth
model User {
  id            String   @id @db.Uuid // This will be the UUID from Supabase Auth
  name          String?
  email         String
  emailVerified DateTime?
  image         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  deletedAt     DateTime? // Soft delete timestamp

  // Relations
  runningProfiles RunningProfile[]

  // Index for faster lookups
  @@index([email, deletedAt], name: "email_deletedAt_idx")
}

// Running Profile model
model RunningProfile {
  id            String   @id @default(cuid()) // System-generated unique identifier
  name          String   // Profile name (User Input)
  createdAt     DateTime @default(now()) // Creation timestamp
  updatedAt     DateTime @updatedAt // Last update timestamp
  isDef<PERSON>     Boolean  @default(false) // Indicates default profile
  isCompleted   <PERSON><PERSON><PERSON>  @default(false) // Indicates if profile creation is completed

  // User relation
  userId String @db.Uuid // User identifier
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Recommendation relation
  recommendation Recommendation?

  // == Runner Profile Data ==
  age                 Int?     // Runner's age (years)
  heightCm            Int?     // Runner's height in cm
  weightKg            Int?     // Runner's weight in kg
  gender              String?  // Gender (Male/Female)
  climate             String?  // Typical climate (Cold/Temperate/Hot/Variable)
  terrain             String?  // Most frequently run surface (Road/Trails/Treadmill/Grass)
  runningGoal         String?  // Training, Racing, Recreational
  previousInjuries    String?  // Previous injuries (categorical string from fixed options)
  previousInjuriesSeverity String? // Severity of previous injuries
  averageWeeklyKm     String?  // Weekly running distance (categorical)
  averagePaceEasyLong String?  // Avg. running pace for easy/long runs
  averageCadence      Int?     // Avg. running cadence (steps/min)

  // == Wearable Data ==
  runningPower        Int?     // Power output during running (Watts)
  groundContactTime   Int?     // Ground contact time (milliseconds)
  verticalOscillationWearable Float? // Vertical oscillation (cm)

  // == Foot Images (URLs) ==
  footImageTopLeftUrl     String?  // URL of top-down image of left foot
  footImageTopRightUrl    String?  // URL of top-down image of right foot
  footImageMedialLeftUrl  String?  // URL of medial side image of left foot
  footImageMedialRightUrl String?  // URL of medial side image of right foot

  // == Foot Imaging (Derived from Images) ==
  heelToToeLengthLeft  Float? // Heel-to-toe length from images - left foot (mm)
  heelToToeLengthRight Float? // Heel-to-toe length from images - right foot (mm)
  forefootWidthLeft    Float? // Forefoot width from images - left foot (mm)
  forefootWidthRight   Float? // Forefoot width from images - right foot (mm)
  medialArchLengthLeft Float? // Medial arch length from images - left foot (mm)
  medialArchLengthRight Float? // Medial arch length from images - right foot (mm)
  medialArchHeightLeft Float? // Medial arch height from images - left foot (mm)
  medialArchHeightRight Float? // Medial arch height from images - right foot (mm)

  // Legacy fields have been removed

  // == Running Videos (URLs) ==
  runningVideoPosteriorUrl String? // URL for posterior view running video
  runningVideoSagittalUrl  String? // URL for sagittal (side) view running video
  runningVideoPosteriorTimestamp Float? // Timestamp for posterior video clip (s)
  runningVideoSagittalTimestamp Float? // Timestamp for sagittal video clip (s)

  // == Running Videos (Derived from Video) ==
  pelvicDrop          Float?  // Pelvic drop direction
  kneeDrift           Float?  // Knee drift (Valgus / Varus)
  footDrift           Float?  // Foot drift/Crossover gait
  heelWhip            Float?  // Medial/Lateral Whip
  posteriorStabilityScore Float? // Stability score from video
  overstride          Float?  // Overstride detection
  ankleDorsiflexion   Float?  // Foot strike type (heelstrike/midfoot/forefoot)
  anklePlantarflexion Float?  // Measures propulsive power and push-off mechanics
  verticalOscillationVideo Float? // Vertical oscillation (cm)
  trunkLean           Float?  // Trunk lean angle (degrees)
  kneeFlexionLoading  Float?  // Knee flexion angle during loading (degrees)
}

// Shoe Model
model ShoeModel {
  id          String   @id @default(cuid())
  brand       String
  name        String
  fullName    String
  modelYear   Int
  category    String
  price       Float
  imageURLs   String[] // Multiple image URLs

  // Basic fields
  description         String
  pros                String[]
  cons                String[]
  rating              Float?
  numberOfReviews     Int     @default(0)

  // Technical specifications
  heelStackHeight     Float?  // Heel stack height in mm
  drop                Float?  // Heel to toe drop in mm
  midsoleWidth        String? // Narrow, Medium, Wide
  stability           String? // Neutral, Stability, Motion Control
  midsoleSoftness     Int?    // Scale 1-10
  forefootFlexibility Int?    // Scale 1-10
  midsoleSoftnessInCold Int?  // Scale 1-10
  toeboxWidth         String? // Toebox width
  toeboxHeight        String? // Toebox height
  type                String? // Brand specification (e.g., Daily Trainer, Racing)
  midsoleFormCategory String? // EVA, TPU, PEBA, etc.
  archSupport         String? // Brand specification
  archType            String? // Low, Medium, High
  pronation           String? // Neutral, Overpronation, Underpronation
  tongueThickness     String? // Thin, Medium, Thick
  torsionalRigidity   Int?    // Scale 1-10
  shoeWeightG         Int?    // Weight in grams
  rockerGeometry      Boolean? // Has rocker geometry
  outsoleThickness    Float?  // Outsole thickness in mm
  terrain             String? // Road, Trail, Track, etc.
  medialLateralPosting Boolean? // Has medial/lateral posting
  tongueType          String? // Gusseted, Standard, etc.

  // New fields
  insoleThickness     Float?  // Insole thickness in mm
  size                Float?  // Shoe size
  widthFit            String? // Width / Fit
  heelCounterStiffness Int?   // Heel counter stiffness
  midsoleWidthForefoot String? // Midsole width - forefoot
  midsoleWidthHeel    String? // Midsole width - heel
  flexibilityStiffness Int?   // Flexibility / Stiffness
  stiffnessInCold     Int?    // Stiffness in cold
  tonguePadding       String? // Tongue padding

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations for recommendations
  recommendationsAsModel1 Recommendation[] @relation("ShoeModel1")
  recommendationsAsModel2 Recommendation[] @relation("ShoeModel2")
  recommendationsAsModel3 Recommendation[] @relation("ShoeModel3")
  recommendationsAsModel4 Recommendation[] @relation("ShoeModel4")
  recommendationsAsModel5 Recommendation[] @relation("ShoeModel5")

  @@unique([brand, name, modelYear])
}

// Recommendation model
model Recommendation {
  id            String   @id @default(cuid())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Running profile relation
  runningProfile   RunningProfile @relation(fields: [runningProfileId], references: [id], onDelete: Cascade)
  runningProfileId String @unique

  // Top 5 shoe recommendations
  shoeModel1Id     String?
  shoeModel1       ShoeModel?  @relation("ShoeModel1", fields: [shoeModel1Id], references: [id], onDelete: SetNull)
  explanation1     String?     // Explanation for first recommendation

  shoeModel2Id     String?
  shoeModel2       ShoeModel?  @relation("ShoeModel2", fields: [shoeModel2Id], references: [id], onDelete: SetNull)
  explanation2     String?     // Explanation for second recommendation

  shoeModel3Id     String?
  shoeModel3       ShoeModel?  @relation("ShoeModel3", fields: [shoeModel3Id], references: [id], onDelete: SetNull)
  explanation3     String?     // Explanation for third recommendation

  shoeModel4Id     String?
  shoeModel4       ShoeModel?  @relation("ShoeModel4", fields: [shoeModel4Id], references: [id], onDelete: SetNull)
  explanation4     String?     // Explanation for fourth recommendation

  shoeModel5Id     String?
  shoeModel5       ShoeModel?  @relation("ShoeModel5", fields: [shoeModel5Id], references: [id], onDelete: SetNull)
  explanation5     String?     // Explanation for fifth recommendation

  // Overall recommendation summary
  overallExplanation String?
}
