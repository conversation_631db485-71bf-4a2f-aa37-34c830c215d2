import { createServiceClient, BUCKET_IMAGES, BUCKET_VIDEOS } from "../lib/supabase-storage";

/**
 * This script updates existing Supabase storage buckets to be private
 * Run with: npx ts-node -r tsconfig-paths/register scripts/update-buckets-to-private.ts
 */
async function updateBucketsToPrivate() {
  try {
    console.log("Updating Supabase storage buckets to be private...");
    
    // Create Supabase client with service role key
    const supabase = createServiceClient();
    
    // Get list of buckets
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error("Error listing buckets:", listError);
      return;
    }
    
    console.log(`Found ${buckets.length} buckets:`, buckets.map(b => b.name).join(", "));
    
    // Update each bucket
    for (const bucket of buckets) {
      if (bucket.name === BUCKET_IMAGES || bucket.name === BUCKET_VIDEOS) {
        console.log(`Updating bucket ${bucket.name} to be private...`);
        
        const { error: updateError } = await supabase.storage.updateBucket(
          bucket.name,
          {
            public: false,
            fileSizeLimit: 50 * 1024 * 1024, // 50MB limit
            allowedMimeTypes: bucket.name === BUCKET_IMAGES 
              ? ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
              : ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm']
          }
        );
        
        if (updateError) {
          console.error(`Error updating bucket ${bucket.name}:`, updateError);
        } else {
          console.log(`Successfully updated bucket ${bucket.name} to be private`);
        }
      } else {
        console.log(`Skipping bucket ${bucket.name} (not a target bucket)`);
      }
    }
    
    console.log("Bucket update process completed");
  } catch (error) {
    console.error("Error updating buckets:", error);
  }
}

// Run the function
updateBucketsToPrivate();
