import { prisma } from "@/lib/prisma";
import { BUCKET_IMAGES, BUCKET_VIDEOS } from "@/lib/supabase-storage";

/**
 * This script migrates existing media URLs in the database to the new storage path format
 * Run with: npx ts-node -r tsconfig-paths/register scripts/migrate-media-urls-to-paths.ts
 */
async function migrateMediaUrlsToPaths() {
  try {
    console.log("Starting migration of media URLs to storage paths...");
    
    // Get all running profiles
    const profiles = await prisma.runningProfile.findMany();
    console.log(`Found ${profiles.length} running profiles to process`);
    
    let updatedCount = 0;
    
    // Process each profile
    for (const profile of profiles) {
      const updates: Record<string, string> = {};
      let hasUpdates = false;
      
      // Check and convert image URLs
      const imageFields = [
        'footImageTopLeftUrl',
        'footImageTopRightUrl',
        'footImageMedialLeftUrl',
        'footImageMedialRightUrl'
      ] as const;
      
      for (const field of imageFields) {
        const url = profile[field];
        if (url && typeof url === 'string' && url.includes('supabase.co') && !url.includes(':')) {
          // Extract the path from the URL
          let path: string | null = null;
          
          if (url.includes('/object/public/')) {
            const match = url.match(/\/object\/public\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('/object/sign/')) {
            const match = url.match(/\/object\/sign\/([^\/]+)\/(.+?)(\?|$)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('/object/authenticated/')) {
            const match = url.match(/\/object\/authenticated\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('fake-foot-image')) {
            // Handle fake data URLs
            path = `${BUCKET_IMAGES}:${url.split('/').pop()}`;
          }
          
          if (path) {
            updates[field] = path;
            hasUpdates = true;
          }
        }
      }
      
      // Check and convert video URLs
      const videoFields = [
        'runningVideoPosteriorUrl',
        'runningVideoSagittalUrl'
      ] as const;
      
      for (const field of videoFields) {
        const url = profile[field];
        if (url && typeof url === 'string' && url.includes('supabase.co') && !url.includes(':')) {
          // Extract the path from the URL
          let path: string | null = null;
          
          if (url.includes('/object/public/')) {
            const match = url.match(/\/object\/public\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('/object/sign/')) {
            const match = url.match(/\/object\/sign\/([^\/]+)\/(.+?)(\?|$)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('/object/authenticated/')) {
            const match = url.match(/\/object\/authenticated\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes('fake-running-video')) {
            // Handle fake data URLs
            path = `${BUCKET_VIDEOS}:${url.split('/').pop()}`;
          }
          
          if (path) {
            updates[field] = path;
            hasUpdates = true;
          }
        }
      }
      
      // Update the profile if there are changes
      if (hasUpdates) {
        await prisma.runningProfile.update({
          where: { id: profile.id },
          data: updates
        });
        
        console.log(`Updated profile ${profile.id} with new storage paths`);
        updatedCount++;
      }
    }
    
    console.log(`Migration complete. Updated ${updatedCount} profiles.`);
  } catch (error) {
    console.error("Error during migration:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateMediaUrlsToPaths();
