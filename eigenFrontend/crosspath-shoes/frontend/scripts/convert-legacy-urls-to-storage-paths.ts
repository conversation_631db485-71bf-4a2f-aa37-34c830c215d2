import { prisma } from "@/lib/prisma";
import { BUCKET_IMAGES, BUCKET_VIDEOS } from "@/lib/supabase-storage";

/**
 * This script converts legacy Supabase URLs in the database to the new storage path format (bucket:filePath)
 * Run with: npx ts-node -r tsconfig-paths/register scripts/convert-legacy-urls-to-storage-paths.ts
 */
async function convertLegacyUrlsToStoragePaths() {
  try {
    console.log("Starting conversion of legacy URLs to storage paths...");

    // Get all running profiles
    const profiles = await prisma.runningProfile.findMany();
    console.log(`Found ${profiles.length} running profiles to process`);

    let updatedCount = 0;

    // Process each profile
    for (const profile of profiles) {
      const updates: Record<string, string> = {};
      let hasUpdates = false;

      // Check and convert image URLs
      const imageFields = [
        "footImageTopLeftUrl",
        "footImageTopRightUrl",
        "footImageMedialLeftUrl",
        "footImageMedialRightUrl",
        "footScanTopPhoto",
        "footScanLeftPhoto",
        "footScanRightPhoto",
        "footScanBackPhoto",
      ] as const;

      for (const field of imageFields) {
        // Use type assertion to handle legacy fields that might not be in the current type
        const url = (profile as any)[field];
        if (
          url &&
          typeof url === "string" &&
          !url.includes(":") &&
          url.includes("supabase.co")
        ) {
          // Extract the path from the URL
          let path: string | null = null;

          if (url.includes("/object/public/")) {
            const match = url.match(/\/object\/public\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("/object/sign/")) {
            const match = url.match(/\/object\/sign\/([^\/]+)\/(.+?)(\?|$)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("/object/authenticated/")) {
            const match = url.match(/\/object\/authenticated\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("fake-foot-image")) {
            // Handle fake data URLs
            path = `${BUCKET_IMAGES}:${url.split("/").pop()}`;
          }

          if (path) {
            updates[field] = path;
            hasUpdates = true;
            console.log(`Converting ${field} from ${url} to ${path}`);
          } else {
            console.warn(`Could not convert URL for ${field}: ${url}`);
          }
        }
      }

      // Check and convert video URLs
      const videoFields = [
        "runningVideoPosteriorUrl",
        "runningVideoSagittalUrl",
        "runningVideoSide",
        "runningVideoBack",
      ] as const;

      for (const field of videoFields) {
        // Use type assertion to handle legacy fields that might not be in the current type
        const url = (profile as any)[field];
        if (
          url &&
          typeof url === "string" &&
          !url.includes(":") &&
          url.includes("supabase.co")
        ) {
          // Extract the path from the URL
          let path: string | null = null;

          if (url.includes("/object/public/")) {
            const match = url.match(/\/object\/public\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("/object/sign/")) {
            const match = url.match(/\/object\/sign\/([^\/]+)\/(.+?)(\?|$)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("/object/authenticated/")) {
            const match = url.match(/\/object\/authenticated\/([^\/]+)\/(.+)/);
            if (match) {
              path = `${match[1]}:${match[2]}`;
            }
          } else if (url.includes("fake-running-video")) {
            // Handle fake data URLs
            path = `${BUCKET_VIDEOS}:${url.split("/").pop()}`;
          }

          if (path) {
            updates[field] = path;
            hasUpdates = true;
            console.log(`Converting ${field} from ${url} to ${path}`);
          } else {
            console.warn(`Could not convert URL for ${field}: ${url}`);
          }
        }
      }

      // Update the profile if there are changes
      if (hasUpdates) {
        await prisma.runningProfile.update({
          where: { id: profile.id },
          data: updates,
        });

        console.log(`Updated profile ${profile.id} with new storage paths`);
        updatedCount++;
      }
    }

    console.log(`Conversion complete. Updated ${updatedCount} profiles.`);
  } catch (error) {
    console.error("Error during conversion:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the conversion
convertLegacyUrlsToStoragePaths();
