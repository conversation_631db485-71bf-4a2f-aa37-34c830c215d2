{"name": "crosspath-shoes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "seed": "node prisma/seed.js"}, "dependencies": {"@auth/neon-adapter": "^1.8.0", "@aws-sdk/client-s3": "^3.787.0", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@neondatabase/serverless": "^1.0.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/bcrypt": "^5.0.2", "@types/three": "^0.174.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^0.27.3", "@vercel/speed-insights": "^1.2.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "lucide-react": "^0.482.0", "next": "^15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-type-animation": "^3.2.0", "sharp": "^0.34.0", "sonner": "^1.7.4", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.17.30", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "5.8.3"}}