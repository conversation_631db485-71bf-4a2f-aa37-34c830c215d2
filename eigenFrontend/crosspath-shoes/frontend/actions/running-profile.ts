"use server";

import { getCurrentUser } from "@/lib/server-auth"; // Use Supabase Auth helper
import { prisma as db } from "@/lib/prisma"; // Try importing prisma from here and alias as db
import { RunningProfile, Prisma } from "@prisma/client";
import {
  uploadToSupabaseS3,
  BUCKET_IMAGES,
  BUCKET_VIDEOS,
} from "@/lib/supabase-storage"; // Import Supabase S3 functions

import sharp from "sharp"; // Import sharp

// No longer need the project reference since we're using storage paths

// Type for the action's return value
interface ActionResult {
  error?: string;
  profileId?: string;
}

/**
 * Creates initial recommendations for a running profile
 * Automatically generates 5 recommendations with shoe models 1-5
 */
async function createInitialRecommendations(profileId: string): Promise<void> {
  try {
    console.log(`Creating initial recommendations for profile ${profileId}`);

    // Check if the shoe models with IDs 1-5 exist
    const shoeModels = await db.shoeModel.findMany({
      where: {
        id: {
          in: ["1", "2", "3", "4", "5"],
        },
      },
      select: { id: true },
    });

    // Log the available shoe models
    console.log(`Found ${shoeModels.length} shoe models for recommendations`);

    // Use raw SQL query to create the recommendation
    // This bypasses any Prisma type issues
    await db.$executeRaw`
      INSERT INTO "Recommendation" (
        "id",
        "runningProfileId",
        "overallExplanation",
        "shoeModel1Id",
        "explanation1",
        "shoeModel2Id",
        "explanation2",
        "shoeModel3Id",
        "explanation3",
        "shoeModel4Id",
        "explanation4",
        "shoeModel5Id",
        "explanation5",
        "createdAt",
        "updatedAt"
      ) VALUES (
        ${`rec_${Date.now()}`},
        ${profileId},
        ${"Based on your running profile, we've selected these shoes that match your needs."},
        ${shoeModels.some((model) => model.id === "1") ? "1" : null},
        ${"This shoe is a great match for your running style with excellent cushioning and support."},
        ${shoeModels.some((model) => model.id === "2") ? "2" : null},
        ${"This shoe provides a balanced combination of stability and flexibility for your running needs."},
        ${shoeModels.some((model) => model.id === "3") ? "3" : null},
        ${"This shoe offers responsive performance with good energy return for your running style."},
        ${shoeModels.some((model) => model.id === "4") ? "4" : null},
        ${"This shoe features a lightweight design with adequate support for your running preferences."},
        ${shoeModels.some((model) => model.id === "5") ? "5" : null},
        ${"This shoe provides durable construction with good traction for various running conditions."},
        NOW(),
        NOW()
      )
    `;

    console.log(
      `Initial recommendations created successfully for profile ${profileId}`
    );
  } catch (error) {
    console.error(
      `Error creating initial recommendations for profile ${profileId}:`,
      error
    );
    // We don't throw the error here to prevent it from affecting the profile creation flow
    // Just log it and continue
  }
}

/**
 * Creates a new running profile or updates the name of an existing one
 * (if an ID is eventually passed, though not used in this initial version).
 * Primarily used by the start page to create the initial record.
 */
export async function createOrUpdateRunningProfile(
  profileName: string,
  existingProfileId?: string // Optional: for potential future updates
): Promise<ActionResult> {
  try {
    // Get user from Supabase Auth
    const user = await getCurrentUser();

    if (!user || !user.id) {
      // Check user ID
      return { error: "Unauthorized: User not logged in." };
    }
    const userId = user.id;

    if (!profileName || profileName.trim().length === 0) {
      return { error: "Profile name cannot be empty." };
    }

    let profile: RunningProfile;
    let isNewProfile = false;

    // For this initial step, we always create a new profile
    // Later, steps might update an existing profile using existingProfileId
    if (existingProfileId) {
      // Logic to update existing profile (maybe only name)
      // profile = await db.runningProfile.update({ where: { id: existingProfileId, userId: user.id }, data: { name: profileName }});
      // For now, let's focus on creation from the start page
      console.warn(
        "Updating existing profile name not fully implemented yet in this action."
      );
      // Fallback to treating it like creation if update logic isn't ready
      profile = await db.runningProfile.update({
        where: { id: existingProfileId, userId: userId }, // Ensure user owns profile
        data: { name: profileName.trim() },
      });
    } else {
      // Create a new profile
      profile = await db.runningProfile.create({
        data: {
          name: profileName.trim(),
          userId: userId,
          // Add any other required default fields if necessary
        },
      });
      isNewProfile = true;
    }

    // If this is a new profile, create initial recommendations
    if (isNewProfile) {
      await createInitialRecommendations(profile.id);
    }

    // Revalidate paths if needed (e.g., a dashboard listing profiles)
    // revalidatePath('/dashboard/running-profiles');

    console.log(`Running profile created/updated with ID: ${profile.id}`);
    return { profileId: profile.id };
  } catch (error) {
    console.error("Error creating/updating running profile:", error);
    if (error instanceof Error) {
      // Basic error handling, could be more specific
      return { error: `Database Error: ${error.message}` };
    }
    return {
      error: "Failed to create or update running profile. Please try again.",
    };
  }
}

// --- Placeholder for File Upload Action ---
// We will need another action later to handle file uploads

// Define the specific type for URL fields that can be updated by this action
// (Should match the types used by the components calling this action)
type UploadableUrlField =
  | "footImageTopLeftUrl"
  | "footImageTopRightUrl"
  | "footImageMedialLeftUrl"
  | "footImageMedialRightUrl"
  | "runningVideoPosteriorUrl"
  | "runningVideoSagittalUrl";

interface UploadFileResult {
  error?: string;
  fileUrl?: string;
  storagePath?: string;
}

export async function uploadProfileMedia(
  profileId: string,
  formData: FormData,
  fieldToUpdate: UploadableUrlField
): Promise<UploadFileResult> {
  const user = await getCurrentUser();
  if (!user || !user.id) {
    return { error: "Unauthorized: User not logged in." };
  }
  const userId = user.id;

  const file = formData.get("file") as File | null;
  if (!file) {
    return { error: "No file provided in FormData." };
  }

  let blobFilename: string;
  let fileBuffer: Buffer | ArrayBuffer | undefined;
  let contentType: string | undefined;
  let isImage = file.type.startsWith("image/");

  try {
    // --- Image Processing (if applicable) ---
    if (isImage) {
      console.log(`Processing image: ${file.name}`);
      const originalBuffer = Buffer.from(await file.arrayBuffer());
      fileBuffer = await sharp(originalBuffer)
        .webp({ quality: 80 }) // Convert to WebP with 80% quality
        .toBuffer();

      // Adjust filename for WebP
      const originalNameWithoutExt = file.name
        .split(".")
        .slice(0, -1)
        .join(".");
      blobFilename = `${userId}/${profileId}/${fieldToUpdate}-${Date.now()}-${originalNameWithoutExt}.webp`;
      contentType = "image/webp";
      console.log(`Image processed to WebP. New filename: ${blobFilename}`);
    } else {
      // For non-images (videos), use original buffer and filename
      fileBuffer = await file.arrayBuffer();
      blobFilename = `${userId}/${profileId}/${fieldToUpdate}-${Date.now()}-${
        file.name
      }`;
      contentType = file.type; // Use original content type for video
      console.log(`Uploading non-image file: ${blobFilename}`);
    }

    if (!fileBuffer) {
      return { error: "Failed to process file buffer." };
    }

    // --- Supabase S3 Upload ---
    console.log(`Uploading ${blobFilename} to Supabase S3...`);

    // Determine which bucket to use based on file type
    const bucket = isImage ? BUCKET_IMAGES : BUCKET_VIDEOS;

    // Upload to Supabase S3 and get file path and signed URL
    const uploadResult = await uploadToSupabaseS3(
      bucket,
      blobFilename,
      fileBuffer,
      contentType || ""
    );

    // Format the storage path for database storage (bucket:filePath format)
    const storagePath = `${uploadResult.bucket}:${uploadResult.filePath}`;

    console.log(`Upload successful. Storage path: ${storagePath}`);
    console.log(`Signed URL (temporary): ${uploadResult.signedUrl}`);

    // --- Update Prisma Record ---
    // Store only the storage path in the database (not the signed URL which expires)
    await db.runningProfile.update({
      where: { id: profileId, userId: userId },
      data: { [fieldToUpdate]: storagePath },
    });

    console.log(
      `Profile ${profileId} updated successfully with storage path for ${fieldToUpdate}`
    );
    // Consider revalidation if needed

    // Return both the storage path and the signed URL for immediate use in the UI
    return {
      fileUrl: uploadResult.signedUrl,
      storagePath: storagePath,
    };
  } catch (error) {
    console.error(
      `Error during media upload/processing for ${fieldToUpdate}:`,
      error
    );
    // Differentiate between processing, upload, or DB errors if needed
    if (error instanceof Error) {
      return { error: `Processing/Upload Error: ${error.message}` };
    }
    return { error: "An unexpected error occurred during upload." };
    // TODO: Consider deleting the uploaded blob if DB update fails
  }
}

// --- Action to Update Wearable/Runner Profile Details ---

// Define the subset of fields this action can update
// Using Prisma.RunningProfileUpdateInput allows flexibility but restricts to valid fields
type ProfileDetailsUpdateInput = Omit<
  Prisma.RunningProfileUpdateInput,
  | "id"
  | "createdAt"
  | "updatedAt"
  | "isDefault"
  | "userId"
  | "user"
  | "recommendations"
  | `${string}Url` // Exclude URL fields handled by uploadProfileMedia
>;

interface UpdateDetailsResult {
  error?: string;
  success?: boolean;
}

export async function updateRunningProfileDetails(
  profileId: string,
  dataToUpdate: ProfileDetailsUpdateInput | Record<string, any>
): Promise<UpdateDetailsResult> {
  const user = await getCurrentUser();
  if (!user || !user.id) {
    return { error: "Unauthorized: User not logged in." };
  }
  const userId = user.id;

  if (!profileId) {
    return { error: "Profile ID is missing." };
  }

  // Check if we need to generate fake data for any fields
  const hasFakeDataPlaceholders = Object.entries(dataToUpdate).some(
    ([_, value]) => value === "fake-data-placeholder"
  );

  // If we have fake data placeholders, we'll handle them specially
  if (hasFakeDataPlaceholders) {
    try {
      // Get the current profile data
      const profile = await db.runningProfile.findUnique({
        where: { id: profileId, userId: userId },
      });

      if (!profile) {
        return { error: "Running profile not found or access denied." };
      }

      // Generate fake data for the fields that need it
      const updatedData = { ...dataToUpdate };
      for (const [key, value] of Object.entries(updatedData)) {
        if (value === "fake-data-placeholder") {
          // Generate fake data based on the field type
          if (key.includes("footImage")) {
            // For foot images, use a storage path format (bucket:filePath)
            const timestamp = Date.now();
            updatedData[
              key as keyof typeof updatedData
            ] = `${BUCKET_IMAGES}:fake-foot-image-${timestamp}.webp`;
          } else if (key.includes("runningVideo")) {
            // For running videos, use a storage path format (bucket:filePath)
            const timestamp = Date.now();
            updatedData[
              key as keyof typeof updatedData
            ] = `${BUCKET_VIDEOS}:fake-running-video-${timestamp}.mp4`;
          }
        }
      }

      console.log(`Updating profile ${profileId} with fake data:`, updatedData);

      // Update the profile with the fake data
      await db.runningProfile.update({
        where: { id: profileId, userId: userId },
        data: updatedData,
      });

      console.log(`Profile ${profileId} updated with fake data successfully.`);
      return { success: true };
    } catch (error) {
      console.error(
        `Error updating profile with fake data for ${profileId}:`,
        error
      );
      if (error instanceof Error) {
        return { error: `Fake Data Error: ${error.message}` };
      }
      return {
        error: "An unexpected error occurred while generating fake data.",
      };
    }
  }

  // If no fake data placeholders, proceed with normal update
  try {
    console.log(`Updating profile ${profileId} with details:`, dataToUpdate);

    await db.runningProfile.update({
      where: { id: profileId, userId: userId }, // Ensure user owns the profile
      data: dataToUpdate, // Pass the validated data object
    });

    console.log(`Profile ${profileId} details updated successfully.`);
    // Consider revalidation if these details are displayed elsewhere immediately
    // revalidatePath(`/running-profile/review`);

    return { success: true };
  } catch (error) {
    console.error(`Error updating profile details for ${profileId}:`, error);
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Handle specific Prisma errors if necessary
      return { error: `Database Error: ${error.code}` };
    }
    if (error instanceof Error) {
      return { error: `Update Error: ${error.message}` };
    }
    return {
      error: "An unexpected error occurred while updating profile details.",
    };
  }
}

// --- Action to Fetch Running Profile Data ---

interface FetchResult {
  profile?: RunningProfile | null; // Use Prisma type directly
  error?: string;
}

export async function getRunningProfileData(
  profileId: string | null
): Promise<FetchResult> {
  if (!profileId) {
    return { error: "Profile ID is required." };
  }

  const user = await getCurrentUser();
  if (!user || !user.id) {
    return { error: "Unauthorized: User not logged in." };
  }
  const userId = user.id;

  try {
    const profile = await db.runningProfile.findUnique({
      where: {
        id: profileId,
        userId: userId, // Ensure the user owns this profile
      },
    });

    if (!profile) {
      return { error: "Running profile not found or access denied." };
    }

    // Return the fetched profile data
    // Note: The context type RunningProfileData is Partial<PrismaRunningProfile> & { name },
    // so returning the full PrismaRunningProfile is compatible.
    return { profile: profile };
  } catch (error) {
    console.error(
      `Error fetching running profile data for ${profileId}:`,
      error
    );
    if (error instanceof Error) {
      return { error: `Database Error: ${error.message}` };
    }
    return {
      error: "An unexpected error occurred while fetching profile data.",
    };
  }
}

// --- Action to Trigger Backend Analysis ---

interface AnalysisResult {
  error?: string;
  message?: string; // e.g., "Analysis started successfully"
}

export async function triggerProfileAnalysis(
  profileId: string
): Promise<AnalysisResult> {
  const user = await getCurrentUser();
  if (!user || !user.id) {
    return { error: "Unauthorized: User not logged in." };
  }
  const userId = user.id;

  if (!profileId) {
    return { error: "Profile ID is missing." };
  }

  try {
    // 1. Fetch the profile data to get necessary URLs and height
    const profile = await db.runningProfile.findUnique({
      where: {
        id: profileId,
        userId: userId, // Ensure user owns the profile
      },
    });

    if (!profile) {
      return { error: "Running profile not found or access denied." };
    }

    // Check if sagittal video is available for analysis
    if (!profile.runningVideoSagittalUrl) {
      console.log(`No sagittal video available for profile ${profileId}, skipping backend analysis`);

      // Generate fake analysis data as fallback
      const fakeAnalysisData = {
        // Foot measurements (in mm) - Left foot
        heelToToeLengthLeft: Math.floor(Math.random() * 50) + 220, // 22-27cm
        forefootWidthLeft: Math.floor(Math.random() * 30) + 80, // 8-11cm
        medialArchLengthLeft: Math.floor(Math.random() * 40) + 160, // 16-20cm
        medialArchHeightLeft: Math.floor(Math.random() * 20) + 15, // 1.5-3.5cm

        // Foot measurements (in mm) - Right foot
        heelToToeLengthRight: Math.floor(Math.random() * 50) + 220, // 22-27cm
        forefootWidthRight: Math.floor(Math.random() * 30) + 80, // 8-11cm
        medialArchLengthRight: Math.floor(Math.random() * 40) + 160, // 16-20cm
        medialArchHeightRight: Math.floor(Math.random() * 20) + 15, // 1.5-3.5cm

        // Running video analysis
        pelvicDrop: Math.random() * 6 - 3, // -3 to 3 degrees
        kneeDrift: Math.random() * 4 - 2, // -2 to 2 cm
        footDrift: Math.random() * 4 - 2, // -2 to 2 cm
        heelWhip: Math.random() * 4 - 2, // -2 to 2 degrees
        posteriorStabilityScore: Math.floor(Math.random() * 30) + 70, // 70-100 score
        overstride: Math.random() * 2, // 0-2 cm
        ankleDorsiflexion: Math.floor(Math.random() * 10) + 10, // 10-20 degrees
        anklePlantarflexion: Math.floor(Math.random() * 10) + 20, // 20-30 degrees
        verticalOscillationVideo: Math.floor(Math.random() * 4) + 6, // 6-10 cm
        trunkLean: Math.floor(Math.random() * 10) + 5, // 5-15 degrees
        kneeFlexionLoading: Math.floor(Math.random() * 15) + 35, // 35-50 degrees

        // Wearable data
        runningPower: Math.floor(Math.random() * 100) + 200, // 200-300 watts
        groundContactTime: Math.floor(Math.random() * 50) + 200, // 200-250 ms
        verticalOscillationWearable: Math.floor(Math.random() * 4) + 6, // 6-10 cm
      };

      // Update the profile with the fake analysis data
      await db.runningProfile.update({
        where: { id: profileId, userId: userId },
        data: fakeAnalysisData,
      });

      return { message: "Analysis completed (no video available for backend processing)" };
    }

    // Call the backend for video analysis
    console.log(`Triggering backend video analysis for profile ${profileId}`);

    try {
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/analyze-video`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          profileId,
          videoUrl: profile.runningVideoSagittalUrl,
          videoType: "sagittal",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Backend analysis failed");
      }

      if (data.success) {
        console.log(`Backend analysis completed successfully for profile ${profileId}`);
        return { message: "AI analysis completed successfully!" };
      } else {
        throw new Error(data.error || "Backend analysis failed");
      }
    } catch (error) {
      console.error(`Backend analysis failed for profile ${profileId}:`, error);

      // Fallback to fake data if backend fails
      console.log(`Falling back to fake data for profile ${profileId}`);

      const fakeAnalysisData = {
        // Foot measurements (in mm) - Left foot
        heelToToeLengthLeft: Math.floor(Math.random() * 50) + 220,
        forefootWidthLeft: Math.floor(Math.random() * 30) + 80,
        medialArchLengthLeft: Math.floor(Math.random() * 40) + 160,
        medialArchHeightLeft: Math.floor(Math.random() * 20) + 15,

        // Foot measurements (in mm) - Right foot
        heelToToeLengthRight: Math.floor(Math.random() * 50) + 220,
        forefootWidthRight: Math.floor(Math.random() * 30) + 80,
        medialArchLengthRight: Math.floor(Math.random() * 40) + 160,
        medialArchHeightRight: Math.floor(Math.random() * 20) + 15,

        // Running video analysis
        pelvicDrop: Math.random() * 6 - 3,
        kneeDrift: Math.random() * 4 - 2,
        footDrift: Math.random() * 4 - 2,
        heelWhip: Math.random() * 4 - 2,
        posteriorStabilityScore: Math.floor(Math.random() * 30) + 70,
        overstride: Math.random() * 2,
        ankleDorsiflexion: Math.floor(Math.random() * 10) + 10,
        anklePlantarflexion: Math.floor(Math.random() * 10) + 20,
        verticalOscillationVideo: Math.floor(Math.random() * 4) + 6,
        trunkLean: Math.floor(Math.random() * 10) + 5,
        kneeFlexionLoading: Math.floor(Math.random() * 15) + 35,

        // Wearable data
        runningPower: Math.floor(Math.random() * 100) + 200,
        groundContactTime: Math.floor(Math.random() * 50) + 200,
        verticalOscillationWearable: Math.floor(Math.random() * 4) + 6,
      };

      // Update the profile with the fake analysis data
      await db.runningProfile.update({
        where: { id: profileId, userId: userId },
        data: fakeAnalysisData,
      });

      return { message: "Analysis completed (backend unavailable, using fallback data)" };
    }
  } catch (error) {
    console.error(`Error triggering analysis for profile ${profileId}:`, error);
    if (error instanceof Error) {
      return { error: `Client-side Error: ${error.message}` };
    }
    return { error: "An unexpected error occurred while triggering analysis." };
  }
}
