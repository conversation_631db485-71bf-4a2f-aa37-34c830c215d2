"use server";

import { getCurrentUser } from "@/lib/server-auth";
import { getSignedUrlServer, getSignedUrlsServer } from "@/lib/media-handler";

/**
 * Server action to generate a signed URL for a Supabase storage file
 * @param url The original Supabase storage URL
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns The signed URL or the original URL if signing fails
 */
export async function getSignedMediaUrl(
  url: string,
  expiresIn: number = 3600
): Promise<{ url: string; error?: string }> {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return { url, error: "Unauthorized: User not logged in." };
    }

    // Generate signed URL
    const signedUrl = await getSignedUrlServer(url, expiresIn);
    return { url: signedUrl };
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return {
      url,
      error: `Failed to generate signed URL: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

/**
 * Server action to generate signed URLs for multiple Supabase storage files
 * @param urls Array of original Supabase storage URLs
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object mapping original URLs to signed URLs
 */
export async function getSignedMediaUrls(
  urls: string[],
  expiresIn: number = 3600
): Promise<{ urls: Record<string, string>; error?: string }> {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return {
        urls: Object.fromEntries(urls.map(url => [url, url])),
        error: "Unauthorized: User not logged in."
      };
    }

    // Generate signed URLs
    const signedUrls = await getSignedUrlsServer(urls, expiresIn);
    return { urls: signedUrls };
  } catch (error) {
    console.error("Error generating signed URLs:", error);
    return {
      urls: Object.fromEntries(urls.map(url => [url, url])),
      error: `Failed to generate signed URLs: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}
