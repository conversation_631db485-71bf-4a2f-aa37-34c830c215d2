import { useState, useEffect } from "react";
import { StoragePath, getMediaUrl } from "@/lib/media-storage";

/**
 * Simple hook for working with media URLs
 * - Handles storage paths and already signed URLs
 * - Automatically caches results
 * - Provides loading and error states
 *
 * @param path The media path (storage path or URL)
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object with URL and loading state
 */
export function useMediaUrl(
  path: StoragePath | string | null | undefined,
  expiresIn: number = 3600
) {
  const [url, setUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(!!path);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Reset state when path changes
    setUrl(null);
    setError(null);

    // Skip if no path
    if (!path) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Fetch the media URL
    getMediaUrl(path, false, expiresIn)
      .then((result) => {
        if (result) {
          setUrl(result);
        } else {
          setError("Failed to generate media URL");
        }
      })
      .catch((err) => {
        console.error("Error in useMediaUrl:", err);
        setError(err instanceof Error ? err.message : "Unknown error");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [path, expiresIn]);

  return {
    url,
    isLoading,
    error,
    isValid: !!url && !error,
  };
}

/**
 * Hook for working with multiple media URLs
 *
 * @param paths Array of media paths
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object with URLs and loading state
 */
export function useMediaUrls(
  paths: (StoragePath | string | null | undefined)[],
  expiresIn: number = 3600
) {
  const [urls, setUrls] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState<boolean>(paths.length > 0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Reset state when paths change
    setUrls({});
    setError(null);

    // Skip if no paths
    const validPaths = paths.filter(
      (path): path is StoragePath | string => !!path
    );

    if (validPaths.length === 0) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Process each path
    Promise.all(
      validPaths.map(async (path) => {
        const key = typeof path === "string" ? path : JSON.stringify(path);
        const url = await getMediaUrl(path, false, expiresIn);
        return { key, url };
      })
    )
      .then((results) => {
        const urlMap: Record<string, string> = {};
        for (const { key, url } of results) {
          if (url) urlMap[key] = url;
        }
        setUrls(urlMap);
      })
      .catch((err) => {
        console.error("Error in useMediaUrls:", err);
        setError(err instanceof Error ? err.message : "Unknown error");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [paths, expiresIn]);

  /**
   * Get a URL for a specific path
   */
  const getUrl = (
    path: StoragePath | string | null | undefined
  ): string | null => {
    if (!path) return null;
    const key = typeof path === "string" ? path : JSON.stringify(path);
    return urls[key] || null;
  };

  return {
    urls,
    getUrl,
    isLoading,
    error,
  };
}

// Legacy aliases for backward compatibility
export const useStorageMedia = useMediaUrl;
export const useStorageMediaBatch = useMediaUrls;
