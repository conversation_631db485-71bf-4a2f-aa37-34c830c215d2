import { useState, useEffect } from "react";
import { getSignedUrls } from "@/lib/media-handler";

/**
 * Hook to get signed URLs for media files
 * @param urls Array of URLs to sign
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object with signed URLs and loading state
 */
export function useSignedMedia(
  urls: (string | null | undefined)[],
  expiresIn: number = 3600
) {
  const [signedUrls, setSignedUrls] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSignedUrls = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await getSignedUrls(urls, expiresIn);
        setSignedUrls(result);
      } catch (err) {
        console.error("Error getting signed URLs:", err);
        setError(err instanceof Error ? err.message : "Failed to get signed URLs");
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch if there are valid URLs
    const validUrls = urls.filter(url => url && url.includes("supabase.co"));
    if (validUrls.length > 0) {
      fetchSignedUrls();
    } else {
      setIsLoading(false);
    }
  }, [urls, expiresIn]);

  /**
   * Get a signed URL for a specific URL
   * @param url The original URL
   * @returns The signed URL if available, otherwise the original URL
   */
  const getUrl = (url: string | null | undefined): string => {
    if (!url) return "";
    return signedUrls[url] || url;
  };

  return {
    signedUrls,
    isLoading,
    error,
    getUrl,
  };
}
