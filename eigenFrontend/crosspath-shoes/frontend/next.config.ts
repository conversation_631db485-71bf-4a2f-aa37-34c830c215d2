import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: [
      "d1muf25xaso8hp.cloudfront.net",
      "a50c43fa19789ebd19f9-1b2661468213da40a8737f058e80edd6.ssl.cf1.rackcdn.com",
      "placehold.co",
      "tmasnbdsrlaotjrpskak.supabase.co", // Supabase storage domain
    ],
  },
  // Configure routes that should always be dynamic (server-rendered)
  // This fixes the "Dynamic server usage" errors during build
  experimental: {
    serverActions: {
      allowedOrigins: ["localhost:3000", "localhost:3001"],
    },
  },
};

export default nextConfig;
