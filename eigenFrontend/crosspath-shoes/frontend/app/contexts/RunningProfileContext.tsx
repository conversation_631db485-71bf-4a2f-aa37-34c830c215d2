"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation"; // Import useRouter
import { RunningProfile as PrismaRunningProfile } from "@prisma/client"; // Import Prisma type with alias

// Define types for fitness tracking data
// These types are for future integration with fitness apps
/*
type RunData = {
  date: string;
  distance: number;
  time: string;
};

type FitnessAppData = {
  weeklyDistance?: number;
  runningFrequency?: string;
  recentRuns?: RunData[];
  [key: string]: unknown;
};
*/

// Align RunningProfileData type with Prisma Model, making specific fields explicit
export type RunningProfileData = Partial<PrismaRunningProfile> & {
  name: string;
  // System Metadata
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  isDefault?: boolean;
  isCompleted?: boolean;
  userId?: string;

  // Runner Profile Data
  age?: number | null;
  heightCm?: number | null;
  weightKg?: number | null;
  gender?: string | null;
  climate?: string | null;
  terrain?: string | null;
  runningGoal?: string | null;
  previousInjuries?: string | null;
  previousInjuriesSeverity?: string | null;
  averageWeeklyKm?: string | null;
  averagePaceEasyLong?: string | null;
  averageCadence?: number | null;

  // Wearable Data
  runningPower?: number | null;
  groundContactTime?: number | null;
  verticalOscillationWearable?: number | null;

  // Foot Imaging
  footImageTopLeftUrl?: string | null;
  footImageTopRightUrl?: string | null;
  footImageMedialLeftUrl?: string | null;
  footImageMedialRightUrl?: string | null;

  // Foot Measurements (Left/Right specific)
  heelToToeLengthLeft?: number | null;
  heelToToeLengthRight?: number | null;
  forefootWidthLeft?: number | null;
  forefootWidthRight?: number | null;
  medialArchLengthLeft?: number | null;
  medialArchLengthRight?: number | null;
  medialArchHeightLeft?: number | null;
  medialArchHeightRight?: number | null;

  // Running Videos
  runningVideoPosteriorUrl?: string | null;
  runningVideoSagittalUrl?: string | null;
  runningVideoPosteriorTimestamp?: number | null;
  runningVideoSagittalTimestamp?: number | null;

  // Running Video Analysis
  pelvicDrop?: number | null;
  kneeDrift?: number | null;
  footDrift?: number | null;
  heelWhip?: number | null;
  posteriorStabilityScore?: number | null;
  overstride?: number | null;
  ankleDorsiflexion?: number | null;
  anklePlantarflexion?: number | null;
  verticalOscillationVideo?: number | null;
  trunkLean?: number | null;
  kneeFlexionLoading?: number | null;
};

// Define the context type
type CreateRunningProfileContextType = {
  profileId: string | null;
  setProfileId: (id: string | null) => void; // Function to set the ID
  profileData: RunningProfileData;
  updateProfileData: (newData: Partial<RunningProfileData>) => Promise<void>; // Make async
  resetProfileData: () => void;
  currentStep: number;
  setCurrentStep: (step: number) => void; // Keep for stepper UI sync
  // submitProfile: () => Promise<string>; // We might not need a single final submit
  steps: { title: string; href: string; description: string }[];
  // connectStrava: () => Promise<void>; // Keep these if needed
  // connectGarmin: () => Promise<void>;
  // connectCoros: () => Promise<void>;
};

// Define steps for the profile creation process
const steps = [
  {
    title: "Start",
    href: "/create-eigen-profile",
    description: "Begin your profile",
  },
  // Foot Imaging Steps
  {
    title: "Foot Image: Top Left",
    href: "/create-eigen-profile/foot-imaging/top-left",
    description: "Photo: Top-down, Left Foot",
  },
  {
    title: "Foot Image: Top Right",
    href: "/create-eigen-profile/foot-imaging/top-right",
    description: "Photo: Top-down, Right Foot",
  },
  {
    title: "Foot Image: Medial Left",
    href: "/create-eigen-profile/foot-imaging/medial-left",
    description: "Photo: Medial Side, Left Foot",
  },
  {
    title: "Foot Image: Medial Right",
    href: "/create-eigen-profile/foot-imaging/medial-right",
    description: "Photo: Medial Side, Right Foot",
  },
  // Running Video Steps
  {
    title: "Running Video: Posterior",
    href: "/create-eigen-profile/running-videos/posterior",
    description: "Video: Posterior (Back) View",
  },
  {
    title: "Running Video: Sagittal",
    href: "/create-eigen-profile/running-videos/sagittal",
    description: "Video: Sagittal (Side) View",
  },
  {
    title: "Runner Info",
    href: "/create-eigen-profile/runner-profile",
    description: "Enter your details",
  },
  {
    title: "Summary & Analysis",
    href: "/create-eigen-profile/summary",
    description: "Review and analyze profile",
  },
  {
    title: "AI Analysis",
    href: "/create-eigen-profile/ai-results",
    description: "Review analysis results",
  },
  {
    title: "Review & Save",
    href: "/create-eigen-profile/review",
    description: "Confirm and save profile",
  },
];

// Create default empty profile
const defaultProfileData: RunningProfileData = {
  name: `New Profile ${new Date().toLocaleDateString()}`,
  // Other fields default to undefined
};

// Create context
const CreateRunningProfileContext = createContext<
  CreateRunningProfileContextType | undefined
>(undefined);

export const CreateRunningProfileProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const router = useRouter(); // Use router for navigation
  const [profileId, setProfileId] = useState<string | null>(null); // Add state for profile ID
  const [profileData, setProfileData] =
    useState<RunningProfileData>(defaultProfileData);
  const [currentStep, setCurrentStep] = useState(0);

  // Set current step based on pathname (client-side effect)
  useEffect(() => {
    if (typeof window !== "undefined") {
      const pathname = window.location.pathname;
      const stepIndex = steps.findIndex((step) =>
        pathname.startsWith(step.href)
      ); // Use startsWith for dynamic routes
      if (stepIndex !== -1) {
        setCurrentStep(stepIndex);
      }
    }
    // We might also want to extract a profileId from the URL here if passed
    // e.g., const urlParams = new URLSearchParams(window.location.search);
    // const idFromUrl = urlParams.get('profileId');
    // if (idFromUrl) setProfileId(idFromUrl);
  }, []); // Run once on mount

  // Update profile data in the context
  const updateProfileData = async (newData: Partial<RunningProfileData>) => {
    // Update local state with the new data
    setProfileData((prev) => {
      const updated = { ...prev, ...newData };
      console.log("Updated profile data in context:", updated);
      return updated;
    });
  };

  // Reset profile data
  const resetProfileData = () => {
    // TODO: Potentially call a server action to delete the profile if it was created?
    setProfileData(defaultProfileData);
    setProfileId(null);
    setCurrentStep(0);
    router.push(steps[0].href); // Navigate back to start
  };

  // Mock functions (keep or remove as needed)
  // const connectStrava = async (): Promise<void> => { ... };
  // const connectGarmin = async (): Promise<void> => { ... };
  // const connectCoros = async (): Promise<void> => { ... };

  // Submit function (maybe remove if handled per step)
  // const submitProfile = async (): Promise<string> => { ... };

  return (
    <CreateRunningProfileContext.Provider
      value={{
        profileId,
        setProfileId,
        profileData,
        updateProfileData,
        resetProfileData,
        currentStep,
        setCurrentStep,
        steps,
        // connectStrava,
        // connectGarmin,
        // connectCoros,
        // submitProfile,
      }}
    >
      {children}
    </CreateRunningProfileContext.Provider>
  );
};

// Custom hook to use the context
export const useRunningProfile = () => {
  const context = useContext(CreateRunningProfileContext);
  if (context === undefined) {
    throw new Error(
      "useRunningProfile must be used within a CreateRunningProfileProvider"
    );
  }
  return context;
};
