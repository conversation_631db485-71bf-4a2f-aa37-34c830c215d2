import "@/app/globals.css";
import type { Metadata } from "next";
import { Space_Grotesk, JetBrains_Mono } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/contexts/AuthContext";
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  variable: "--font-space-grotesk",
  display: "swap",
});

const jetBrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains-mono",
  weight: ["300", "400"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "eigen - Find Your Perfect Running Shoes",
  description:
    "AI-powered running shoe recommendations based on your unique foot structure, running mechanics, and preferences.",
  metadataBase: new URL("https://eigen.crosspath-labs.com"),
  openGraph: {
    title: "eigen",
    description:
      "AI-powered running shoe recommendations based on your unique foot structure, running mechanics, and preferences.",
    url: "https://eigen.crosspath-labs.com",
    siteName: "eigen",
    images: [
      {
        url: "/logo.svg",
        width: 1200,
        height: 630,
        alt: "eigen",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "eigen",
    description:
      "AI-powered running shoe recommendations based on your unique foot structure, running mechanics, and preferences.",
    images: ["/logo.svg"],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${spaceGrotesk.variable} ${jetBrainsMono.variable} font-sans bg-background text-foreground`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
          forcedTheme="light"
        >
          <AuthProvider>{children}</AuthProvider>
        </ThemeProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
