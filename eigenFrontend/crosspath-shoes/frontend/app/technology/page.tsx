"use client";

import { TypeAnimation } from "react-type-animation";
import { motion } from "framer-motion";
import Image from "next/image";
import { LandingHeader } from "@/components/landing-header";
import PoseDetection from "@/components/PoseDetection";
import {
  Shield,
  Zap,
  Footprints,
  BarChart3,
  Key,
  Activity,
} from "lucide-react";

// Animation variants
const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

// Tech icon component
const TechIcon = ({
  name,
  className,
}: {
  name: string;
  className?: string;
}) => {
  return (
    <div
      className={`flex items-center justify-center p-4 border-2 bg-background/80 backdrop-blur-sm ${
        className || "border-border text-foreground"
      }`}
    >
      {name === "key" && <Key className="w-full h-full" />}
      {name === "muscle" && <Activity className="w-full h-full" />}
      {name === "chart" && <BarChart3 className="w-full h-full" />}
      {name === "shield" && <Shield className="w-full h-full" />}
      {name === "footprints" && <Footprints className="w-full h-full" />}
      {name === "zap" && <Zap className="w-full h-full" />}
    </div>
  );
};

export default function TechnologyPage() {
  return (
    <div className="flex flex-col min-h-screen font-mono overflow-x-hidden bg-background text-foreground relative">
      {/* Subtle background pattern - Example using grid */}
      <div className="absolute inset-0 w-full h-full bg-grid-pattern-light dark:bg-grid-pattern bg-grid-size opacity-[0.03] dark:opacity-[0.02] animate-subtle-pulse pointer-events-none"></div>

      {/* Use the LandingHeader component */}
      <LandingHeader />

      {/* Main content needs z-index to be above background */}
      <main className="flex-1 z-10">
        {/* What is eigen Section */}
        <motion.section
          className="relative py-16 sm:py-20 bg-background border-t border-b border-border/20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-5xl mx-auto px-4 sm:px-6 z-10 relative">
            <div className="grid md:grid-cols-2 gap-8 items-start">
              <motion.div
                variants={fadeInVariant}
                className="p-6 sm:p-8 bg-background border border-border rounded-lg shadow-xl relative z-10 overflow-hidden"
              >
                <div className="relative">
                  <h2 className="font-sans text-3xl sm:text-4xl font-medium text-foreground mb-6 flex items-center">
                    <span className="text-primary/80 mr-2">//</span> What is{" "}
                    <span className="text-primary ml-2 relative">
                      eigen
                      <motion.span
                        className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary/50"
                        initial={{ width: 0 }}
                        whileInView={{ width: "100%" }}
                        viewport={{ once: true }}
                        transition={{ duration: 1, ease: "easeOut" }}
                      ></motion.span>
                    </span>
                    ?
                  </h2>
                  <div className="space-y-4 font-jetbrains text-sm sm:text-base text-muted-foreground backdrop-blur-sm bg-background/50 p-4 rounded-md border border-border/30">
                    <p>
                      &gt; A biomechanical analysis platform that matches your
                      unique running characteristics to the perfect shoes.
                    </p>
                    <p>
                      &gt; We analyze foot structure, gait patterns, and running
                      preferences to create your personalized profile.
                    </p>
                    <p>
                      &gt; Our algorithms identify the precise shoe
                      characteristics that will optimize your performance and
                      reduce injury risk.
                    </p>
                  </div>

                  <div className="mt-8 relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-background via-transparent to-background z-10 pointer-events-none"></div>
                    <Image
                      src="/images/eigen_runner_black_white.png"
                      alt="Eigen Runner Analysis"
                      width={400}
                      height={300}
                      className="rounded-md shadow-lg object-contain mx-auto md:mx-0 transition-all duration-500 hover:scale-105"
                    />
                  </div>
                </div>
              </motion.div>

              <motion.div variants={fadeInVariant}>
                <div className="bg-background border border-border rounded-lg shadow-xl overflow-hidden">
                  <div className="p-4 bg-muted/30 border-b border-border flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-xs font-jetbrains text-muted-foreground">
                      eigen-profile-generator.js
                    </div>
                    <div className="w-16"></div>
                  </div>

                  <div className="relative">
                    <motion.div className="p-5 sm:p-6 text-xs sm:text-sm text-left overflow-x-auto font-input relative group">
                      {/* Line numbers */}
                      <div className="absolute left-0 top-0 bottom-0 w-8 flex flex-col items-end pr-2 text-gray-500 select-none">
                        {Array.from({ length: 20 }).map((_, i) => (
                          <div key={i} className="h-6 text-[10px]">
                            {i + 1}
                          </div>
                        ))}
                      </div>

                      <pre className="whitespace-pre-wrap relative z-10 pl-8">
                        <motion.code
                          className="language-javascript text-gray-300 block"
                          initial={{ opacity: 0.5 }}
                          whileInView={{ opacity: 1 }}
                          transition={{ duration: 1 }}
                        >{`// eigen Profile Generator v1.2.3
// ---------------------------------

async function createRunningProfile(userData) {
  console.log("Initializing profile generation...");

  const { footScan, gaitVideo, metrics, preferences } = userData;
  validateInput(userData);

  const biomechanics = await analyzeBiomechanics(footScan, gaitVideo);
  // => { pronation: -2.7, archType: "medium", ... }

  const riskFactors = calculateRiskProfile(biomechanics, metrics);
  // => { kneeStress: "low", ankleStability: "moderate" }

  const shoeSpec = generateShoeRequirements(biomechanics, riskFactors, preferences);
  // => { stability: 8.5, cushioning: 6.2, ... }

  const matches = await findOptimalShoes(shoeSpec);
  return { profile: biomechanics, recommendations: matches };
}`}</motion.code>
                      </pre>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>

        {/* eigenVALUES Section */}
        <motion.section
          id="eigenvalues"
          className="relative py-14 sm:py-20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={staggerVariant}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 z-10 relative">
            <motion.div
              className="text-center mb-10 sm:mb-14"
              variants={fadeInVariant}
            >
              <h2 className="font-sans text-3xl sm:text-4xl md:text-5xl font-semibold text-foreground mb-4">
                {"// "}eigen<span className="text-primary">VALUES</span>
              </h2>
              <p className="font-jetbrains text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                &gt; In mathematics, an eigenvalue identifies what remains
                invariant during a transformation. We identify what makes your
                running unique.
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
              variants={staggerVariant}
            >
              {[
                {
                  term: "eigenvalues",
                  description:
                    "Characteristic values invariant under transformation.",
                  value:
                    "We identify the unique, unchanging aspects of your running form.",
                  icon: <Zap className="w-7 h-7 text-primary mb-3" />,
                  color: "from-primary/20 to-primary/10",
                  borderColor: "border-primary/30",
                  hoverColor:
                    "group-hover:from-primary/30 group-hover:to-primary/20",
                },
                {
                  term: "eigenvectors",
                  description:
                    "Directions that remain unchanged during transformation.",
                  value:
                    "We analyze the directional forces in your stride that define your movement pattern.",
                  icon: <Footprints className="w-7 h-7 text-primary mb-3" />,
                  color: "from-primary/20 to-primary/10",
                  borderColor: "border-primary/30",
                  hoverColor:
                    "group-hover:from-primary/30 group-hover:to-primary/20",
                },
                {
                  term: "eigenfunctions",
                  description:
                    "Functions that maintain their form under transformation.",
                  value:
                    "We map how your body's movement patterns maintain consistency across different conditions.",
                  icon: <BarChart3 className="w-7 h-7 text-primary mb-3" />,
                  color: "from-primary/20 to-primary/10",
                  borderColor: "border-primary/30",
                  hoverColor:
                    "group-hover:from-primary/30 group-hover:to-primary/20",
                },
              ].map((item, index) => (
                <motion.div
                  key={index}
                  variants={fadeInVariant}
                  className="group p-6 rounded-lg border bg-gradient-to-br transition-all duration-300 hover:shadow-lg"
                  style={{
                    borderColor: `var(--${item.borderColor})`,
                    background: `linear-gradient(to bottom right, var(--${item.color
                      .split(" ")[0]
                      .slice(5)}), var(--${item.color
                      .split(" ")[1]
                      .slice(3)}))`,
                  }}
                >
                  <div className="mb-4">{item.icon}</div>
                  <h3 className="font-sans text-xl font-semibold mb-2 text-foreground">
                    {item.term}
                  </h3>
                  <p className="text-xs text-muted-foreground mb-3 font-jetbrains italic">
                    {item.description}
                  </p>
                  <p className="text-sm text-foreground font-jetbrains">
                    {item.value}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </motion.section>

        {/* New Pose Detection Section */}
        <motion.section
          id="live-pose-analysis"
          className="relative py-14 sm:py-16 bg-gray-900/5 dark:bg-black/20 border-t border-border/10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 z-10 relative">
            <PoseDetection />
          </div>
        </motion.section>

        {/* Footer with enhanced design */}
        <footer className="relative p-5 sm:p-6 bg-popover/90 backdrop-blur-sm border-t border-primary/10 z-10">
          <div className="max-w-7xl mx-auto text-center font-light text-xs sm:text-sm text-muted-foreground space-y-2 px-4 sm:px-6">
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="font-mono"
            >
              <span className="text-primary/80 mr-1">//</span> &copy;{" "}
              {new Date().getFullYear()} CrossPath Labs{" "}
              <span className="text-primary/80">eigen</span> Division
            </motion.p>

            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.8 }}
              className="max-w-xl mx-auto font-jetbrains"
            >
              Transparency: Equal, standardized affiliate payment
              post-recommendation. Your data drives results, not brand deals.
            </motion.p>
          </div>
        </footer>
      </main>
    </div>
  );
}
