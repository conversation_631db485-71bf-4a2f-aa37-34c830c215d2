import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";

// Backend API URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || "http://localhost:8000";

/**
 * GET /api/video-status
 * Checks the status of a video processing task
 */
export async function GET(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get task ID from query params
    const taskId = req.nextUrl.searchParams.get("taskId");
    if (!taskId) {
      return NextResponse.json(
        { error: "Missing taskId parameter" },
        { status: 400 }
      );
    }

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_API_URL}/video_status/${taskId}`);

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        {
          error: `Backend error: ${errorData.error || response.statusText}`,
        },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();

    // If the video is ready, construct the full URL
    if (data.processed_video_url) {
      data.processed_video_url = `${BACKEND_API_URL}${data.processed_video_url}`;
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error checking video status:", error);
    return NextResponse.json(
      {
        error: `An error occurred while checking video status: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
