import { NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";

// Backend API URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || "http://localhost:8000";

/**
 * GET /api/list-videos
 * Lists available videos from the backend
 */
export async function GET() {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_API_URL}/list_videos`);

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        {
          error: `Backend error: ${errorData.error || response.statusText}`,
        },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error listing videos:", error);
    return NextResponse.json(
      {
        error: `An error occurred while listing videos: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
