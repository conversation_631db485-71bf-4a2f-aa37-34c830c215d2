import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";
import { getMediaUrlServer } from "@/lib/media-storage";

// Backend API URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || "http://localhost:8000";

/**
 * POST /api/analyze-video
 * Triggers video analysis for a running profile's sagittal video
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { profileId, videoUrl, videoType = "sagittal" } = body;

    if (!profileId || !videoUrl) {
      return NextResponse.json(
        { error: "Missing required fields: profileId and videoUrl" },
        { status: 400 }
      );
    }

    // Get a signed URL for the video if it's a storage path
    const signedVideoUrl = await getMediaUrlServer(videoUrl);
    if (!signedVideoUrl) {
      return NextResponse.json(
        { error: "Failed to generate signed URL for video" },
        { status: 500 }
      );
    }

    console.log(`🚀 Starting video analysis for profile ${profileId}, video type: ${videoType}`);

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_API_URL}/analyze-url`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        video_url: signedVideoUrl,
        profile_id: profileId,
        video_type: videoType,
        user_id: user.id,
        visualize_results: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error(`❌ Backend analysis failed for profile ${profileId}:`, errorData);
      return NextResponse.json(
        {
          error: `Backend error: ${errorData.error || response.statusText}`,
        },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Video analysis completed successfully for profile ${profileId}`);
    } else {
      console.error(`❌ Video analysis failed for profile ${profileId}:`, data.error);
    }

    // Return response with additional metadata
    return NextResponse.json({
      success: data.success,
      message: data.message,
      profileId,
      videoType,
      analysisComplete: data.success,
      processingTime: data.processing_time,
      error: data.error,
    });
  } catch (error) {
    console.error("Error in video analysis API:", error);
    return NextResponse.json(
      {
        error: `An error occurred while processing the video: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
