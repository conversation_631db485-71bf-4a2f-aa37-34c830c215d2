import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";
import { getSignedUrlServer } from "@/lib/media-handler";

/**
 * GET /api/media/signed-url?url=...
 * Generates a signed URL for a Supabase storage file
 */
export async function GET(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get URL from query params
    const url = req.nextUrl.searchParams.get("url");
    if (!url) {
      return NextResponse.json(
        { error: "URL parameter is required" },
        { status: 400 }
      );
    }

    // Get expiration time from query params (default: 1 hour)
    const expiresInStr = req.nextUrl.searchParams.get("expiresIn");
    const expiresIn = expiresInStr ? parseInt(expiresInStr, 10) : 3600;

    // Generate signed URL
    const signedUrl = await getSignedUrlServer(url, expiresIn);

    return NextResponse.json({ url: signedUrl });
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return NextResponse.json(
      {
        error: `An error occurred while generating signed URL: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/media/signed-url
 * Generates signed URLs for multiple Supabase storage files
 * Body: { urls: string[], expiresIn?: number }
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { urls, expiresIn = 3600 } = body;

    if (!urls || !Array.isArray(urls)) {
      return NextResponse.json(
        { error: "URLs array is required in request body" },
        { status: 400 }
      );
    }

    // Generate signed URLs
    const result: Record<string, string> = {};
    for (const url of urls) {
      if (typeof url === "string" && url.includes("supabase.co")) {
        result[url] = await getSignedUrlServer(url, expiresIn);
      } else {
        result[url] = url;
      }
    }

    return NextResponse.json({ urls: result });
  } catch (error) {
    console.error("Error generating signed URLs:", error);
    return NextResponse.json(
      {
        error: `An error occurred while generating signed URLs: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
