import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";

// Backend API URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || "http://localhost:8000";

/**
 * POST /api/process-video
 * Processes a video with MediaPipe Pose by forwarding the request to the backend
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { filename, profile_id, video_type } = body;

    if (!filename || !profile_id || !video_type) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_API_URL}/process_video`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        filename,
        profile_id,
        video_type,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        {
          error: `Backend error: ${errorData.error || response.statusText}`,
        },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error processing video:", error);
    return NextResponse.json(
      {
        error: `An error occurred while processing the video: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
