import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";

const prisma = new PrismaClient();

// Helper function to get the current user from Supabase Auth
async function getCurrentUser() {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return null;
  }

  return user;
}

/**
 * GET /api/running-profiles/list
 * Returns optimized running profiles for the running-profiles page
 * Only includes essential data and only generates a signed URL for the main profile's sagittal video
 */
export async function GET() {
  try {
    // Get the current user from Supabase Auth
    const supabaseUser = await getCurrentUser();

    if (!supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    // Get all running profiles for this user
    const runningProfiles = await prisma.runningProfile.findMany({
      where: { userId: user.id },
      orderBy: { updatedAt: "desc" },
      select: {
        // Only select the fields we need for the running-profiles page
        id: true,
        name: true,
        createdAt: true,
        updatedAt: true,
        isDefault: true,
        runningGoal: true,
        runningVideoSagittalUrl: true,
        recommendation: {
          select: {
            id: true,
            shoeModel1Id: true,
            shoeModel1: {
              select: {
                id: true,
                name: true,
              },
            },
            shoeModel2Id: true,
            shoeModel2: {
              select: {
                id: true,
                name: true,
              },
            },
            shoeModel3Id: true,
            shoeModel3: {
              select: {
                id: true,
                name: true,
              },
            },
            shoeModel4Id: true,
            shoeModel4: {
              select: {
                id: true,
                name: true,
              },
            },
            shoeModel5Id: true,
            shoeModel5: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Find the default profile or use the first one
    const mainProfile =
      runningProfiles.find((p) => p.isDefault) || runningProfiles[0];

    // Import the function to generate media URLs
    const { getMediaUrlServer } = await import("@/lib/media-storage");

    // Process the profiles to generate a signed URL only for the main profile's sagittal video
    const processedProfiles = await Promise.all(
      runningProfiles.map(async (profile) => {
        // Create a copy of the profile to avoid modifying the original
        const processedProfile = { ...profile };

        // Only generate a signed URL for the main profile's sagittal video
        if (
          mainProfile &&
          profile.id === mainProfile.id &&
          profile.runningVideoSagittalUrl &&
          typeof profile.runningVideoSagittalUrl === "string" &&
          profile.runningVideoSagittalUrl.includes(":")
        ) {
          try {
            const mediaUrl = await getMediaUrlServer(
              profile.runningVideoSagittalUrl
            );
            if (mediaUrl) {
              processedProfile.runningVideoSagittalUrl = mediaUrl;
            }
          } catch (error) {
            console.error(
              `Error generating signed URL for main profile video:`,
              error
            );
          }
        } else {
          // For non-main profiles, don't include the video URL at all
          processedProfile.runningVideoSagittalUrl = null;
        }

        return processedProfile;
      })
    );

    return NextResponse.json(
      {
        runningProfiles: processedProfiles,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profiles error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving running profiles" },
      { status: 500 }
    );
  }
}
