import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  console.log("Auth callback called with code:", code ? "present" : "missing");

  if (code) {
    const supabase = await createClient();
    const sessionResult = await supabase.auth.exchangeCodeForSession(code);

    console.log("Exchange code for session result:", {
      session: sessionResult.data.session ? "present" : "missing",
      error: sessionResult.error,
    });

    // Get the user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    console.log("Get user result:", {
      user: user ? { id: user.id, email: user.email } : "missing",
      error: userError,
    });

    // We don't need to create a User record here anymore since we're creating it at signup time
    // This is just a fallback in case the User record wasn't created during signup
    if (user) {
      // Check if User record already exists
      const { data: existingUser, error: userQueryError } = await supabase
        .from("User")
        .select("*")
        .eq("id", user.id)
        .single();

      console.log("User check result:", {
        existingUser: existingUser ? "found" : "not found",
        error: userQueryError,
      });

      // Only create User record if it doesn't exist
      if (!existingUser) {
        console.log("Creating User record for user:", user.id);
        try {
          const { data: newUser, error: userError } = await supabase
            .from("User")
            .insert([
              {
                id: user.id,
                name:
                  user.user_metadata.name ||
                  user.email?.split("@")[0] ||
                  "User",
                email: user.email,
              },
            ])
            .select()
            .single();

          console.log("User creation result:", {
            user: newUser ? "created" : "not created",
            error: userError,
          });

          if (userError) {
            console.error("Error creating User record:", userError);
          }
        } catch (error) {
          console.error("Error creating User record:", error);
        }
      }
    }
  }

  console.log("Redirecting to signin page");
  // Redirect to the signin page
  return NextResponse.redirect(new URL("/signin", request.url));
}
