"use client";

import { motion } from "framer-motion";
import { LandingHeader } from "@/components/landing-header";
import RunningCards from "@/components/landing/RunningCards";
import { HeroSection } from "@/components/landing/HeroSection";
import { WhatIsEigenSection } from "@/components/landing/WhatIsEigenSection";
import { FoundersSection } from "@/components/landing/FoundersSection";
import { CTASection } from "@/components/landing/CTASection";
import { LandingFooter } from "@/components/landing/LandingFooter";

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen font-mono overflow-x-hidden bg-white text-foreground relative">
      {/* Use the LandingHeader component */}
      <LandingHeader />

      {/* Main content needs z-index to be above background */}
      <main className="flex-1 z-10">
        <HeroSection />

        {/* Feature Cards Section */}
        <motion.section
          className="relative py-12 sm:py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-7xl mx-auto z-10 relative px-4">
            <RunningCards />
          </div>
        </motion.section>

        <WhatIsEigenSection />
        <FoundersSection />
        <CTASection />
      </main>

      <LandingFooter />
    </div>
  );
}
