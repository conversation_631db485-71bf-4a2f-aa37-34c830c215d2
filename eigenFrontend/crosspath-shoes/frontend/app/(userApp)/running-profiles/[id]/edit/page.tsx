"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  ChevronLeft, 
  Save,
  Loader2
} from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

// Interface for running profile
interface RunningProfile {
  id: string;
  name: string;
  isDefault: boolean;
  // Foot scan data
  footScanVideo: string | null;
  gaitVideo: string | null;
  footLength: number | null;
  footWidth: number | null;
  archType: string | null;
  pronationType: string | null;
  footScanNotes: string | null;
  // Athletic data
  runningFrequency: string | null;
  weeklyDistance: number | null;
  runningGoals: string[];
  terrainTypes: string[];
  previousInjuries: string[];
  // Measurements
  weight: number | null;
  height: number | null;
  shoeSize: number | null;
  currentShoes: string | null;
  currentShoeIssues: string[];
  // Preferences
  cushioningPreference: string | null;
  weightPreference: string | null;
  supportPreference: string | null;
  dropPreference: string | null;
  colorPreference: string | null;
  importantFactors: string[];
}

export default function EditProfilePage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = React.use(params);
  const { id } = resolvedParams;
  const router = useRouter();
  
  const [profile, setProfile] = useState<RunningProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch profile data
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/running-profiles/${id}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch running profile");
        }
        
        const data = await response.json();
        setProfile(data.runningProfile);
      } catch (err) {
        console.error("Error fetching profile:", err);
        setError(err instanceof Error ? err.message : "Failed to load running profile");
      } finally {
        setLoading(false);
      }
    };
    
    fetchProfile();
  }, [id]);

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (profile) {
      setProfile({
        ...profile,
        [name]: value,
      });
    }
  };

  // Handle number input changes
  const handleNumberChange = (name: string, value: string) => {
    if (profile) {
      setProfile({
        ...profile,
        [name]: value === "" ? null : parseFloat(value),
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string | null) => {
    if (profile) {
      setProfile({
        ...profile,
        [name]: value === "_none" ? null : value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!profile) return;
    
    try {
      setSaving(true);
      
      const response = await fetch(`/api/running-profiles/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(profile),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update profile");
      }
      
      router.push(`/running-profiles/${id}`);
    } catch (err) {
      console.error("Error updating profile:", err);
      setError(err instanceof Error ? err.message : "Failed to update profile");
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div>
        <div className="rounded-md bg-destructive/10 p-4 text-destructive">
          <p>{error || "Profile not found"}</p>
        </div>
        <Link href="/running-profiles">
          <Button variant="ghost" className="mt-4">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Profiles
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href={`/running-profiles/${id}`}>
            <Button variant="ghost" size="icon">
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Profile</h1>
            <p className="text-muted-foreground">
              Update your running profile details
            </p>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="rounded-md bg-destructive/10 p-4 text-destructive mb-6">
          <p>{error}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList>
            <TabsTrigger value="basic">Basic Information</TabsTrigger>
            <TabsTrigger value="foot">Foot Scan Data</TabsTrigger>
            <TabsTrigger value="activity">Running Activity</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Basic details about your running profile
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Profile Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={profile.name}
                    onChange={handleChange}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isDefault"
                    checked={profile.isDefault}
                    onCheckedChange={(checked) => {
                      setProfile({
                        ...profile,
                        isDefault: checked === true,
                      });
                    }}
                  />
                  <label
                    htmlFor="isDefault"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Set as default profile
                  </label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="foot">
            <Card>
              <CardHeader>
                <CardTitle>Foot Scan Data</CardTitle>
                <CardDescription>
                  Details about your foot measurements and scan data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="footLength">Foot Length (cm)</Label>
                    <Input
                      id="footLength"
                      type="number"
                      step="0.1"
                      value={profile.footLength !== null ? profile.footLength : ''}
                      onChange={(e) => handleNumberChange("footLength", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="footWidth">Foot Width (cm)</Label>
                    <Input
                      id="footWidth"
                      type="number"
                      step="0.1"
                      value={profile.footWidth !== null ? profile.footWidth : ''}
                      onChange={(e) => handleNumberChange("footWidth", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="archType">Arch Type</Label>
                    <Select
                      value={profile.archType || "_none"}
                      onValueChange={(value) => handleSelectChange("archType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select arch type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="pronationType">Pronation Type</Label>
                    <Select
                      value={profile.pronationType || "_none"}
                      onValueChange={(value) => handleSelectChange("pronationType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select pronation type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="neutral">Neutral</SelectItem>
                        <SelectItem value="overpronation">Overpronation</SelectItem>
                        <SelectItem value="underpronation">Underpronation (Supination)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="footScanNotes">Notes</Label>
                  <Textarea
                    id="footScanNotes"
                    name="footScanNotes"
                    value={profile.footScanNotes || ''}
                    onChange={handleChange}
                    placeholder="Any additional notes about your foot scan..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="footScanVideo">Foot Scan Video URL</Label>
                  <Input
                    id="footScanVideo"
                    name="footScanVideo"
                    value={profile.footScanVideo || ''}
                    onChange={handleChange}
                    placeholder="https://example.com/video.mp4"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="gaitVideo">Running Gait Video URL</Label>
                  <Input
                    id="gaitVideo"
                    name="gaitVideo"
                    value={profile.gaitVideo || ''}
                    onChange={handleChange}
                    placeholder="https://example.com/gait-video.mp4"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle>Running Activity</CardTitle>
                <CardDescription>
                  Information about your running habits and measurements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="runningFrequency">Running Frequency</Label>
                    <Select
                      value={profile.runningFrequency || "_none"}
                      onValueChange={(value) => handleSelectChange("runningFrequency", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="4-6 times per week">4-6 times per week</SelectItem>
                        <SelectItem value="2-3 times per week">2-3 times per week</SelectItem>
                        <SelectItem value="once a week">Once a week</SelectItem>
                        <SelectItem value="occasionally">Occasionally</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="weeklyDistance">Weekly Distance (miles)</Label>
                    <Input
                      id="weeklyDistance"
                      type="number"
                      step="0.1"
                      value={profile.weeklyDistance !== null ? profile.weeklyDistance : ''}
                      onChange={(e) => handleNumberChange("weeklyDistance", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="height">Height (cm)</Label>
                    <Input
                      id="height"
                      type="number"
                      step="0.1"
                      value={profile.height !== null ? profile.height : ''}
                      onChange={(e) => handleNumberChange("height", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="weight">Weight (kg)</Label>
                    <Input
                      id="weight"
                      type="number"
                      step="0.1"
                      value={profile.weight !== null ? profile.weight : ''}
                      onChange={(e) => handleNumberChange("weight", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="shoeSize">Shoe Size (US)</Label>
                    <Input
                      id="shoeSize"
                      type="number"
                      step="0.5"
                      value={profile.shoeSize !== null ? profile.shoeSize : ''}
                      onChange={(e) => handleNumberChange("shoeSize", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="currentShoes">Current Running Shoes</Label>
                    <Input
                      id="currentShoes"
                      name="currentShoes"
                      value={profile.currentShoes || ''}
                      onChange={handleChange}
                      placeholder="e.g., Nike Pegasus 40"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>
                  Your preferences for running shoes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cushioningPreference">Cushioning Preference</Label>
                    <Select
                      value={profile.cushioningPreference || "_none"}
                      onValueChange={(value) => handleSelectChange("cushioningPreference", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select cushioning preference" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="moderate">Moderate</SelectItem>
                        <SelectItem value="maximum">Maximum</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="weightPreference">Weight Preference</Label>
                    <Select
                      value={profile.weightPreference || "_none"}
                      onValueChange={(value) => handleSelectChange("weightPreference", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select weight preference" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="lightweight">Lightweight</SelectItem>
                        <SelectItem value="balanced">Balanced</SelectItem>
                        <SelectItem value="sturdy">Sturdy</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="supportPreference">Support Preference</Label>
                    <Select
                      value={profile.supportPreference || "_none"}
                      onValueChange={(value) => handleSelectChange("supportPreference", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select support preference" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="neutral">Neutral</SelectItem>
                        <SelectItem value="stability">Stability</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="dropPreference">Drop Preference</Label>
                    <Select
                      value={profile.dropPreference || "_none"}
                      onValueChange={(value) => handleSelectChange("dropPreference", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select drop preference" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_none">Not specified</SelectItem>
                        <SelectItem value="zero">Zero Drop</SelectItem>
                        <SelectItem value="low">Low (1-4mm)</SelectItem>
                        <SelectItem value="medium">Medium (5-8mm)</SelectItem>
                        <SelectItem value="high">High (9mm+)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="colorPreference">Color Preference</Label>
                    <Input
                      id="colorPreference"
                      name="colorPreference"
                      value={profile.colorPreference || ''}
                      onChange={handleChange}
                      placeholder="e.g., Black, Red, Blue"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-end mt-6">
          <div className="space-x-2">
            <Button 
              variant="outline" 
              type="button"
              onClick={() => router.push(`/running-profiles/${id}`)}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={saving}
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Profile
                </>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
