"use client";

import { useEffect, useState, useRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  UserRound,
  PlusCircle,
  Trash2,
  Star,
  ArrowRight,
  PlayCircle,
  Loader2,
  Video,
  X,
  Calendar,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { RunningProfile } from "@/lib/types";
import { StorageMedia } from "@/components/ui/storage-media";
import { ProfileDetailView } from "@/components/running-profile/ProfileDetailView";
import { CreateRunningProfileProvider } from "@/app/contexts/RunningProfileContext";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { format } from "date-fns";

export default function RunningProfilesPage() {
  const [profiles, setProfiles] = useState<RunningProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<RunningProfile | null>(
    null
  );
  const [currentSlide, setCurrentSlide] = useState(0);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const isMobile = useMediaQuery("(max-width: 639px)");
  const modalRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchProfiles = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch("/api/running-profiles/list");
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || "Failed to fetch running profiles"
          );
        }
        const data = await response.json();
        // Sort profiles to show default profile first
        const sortedProfiles = [...data.runningProfiles].sort((a, b) => {
          if (a.isDefault) return -1;
          if (b.isDefault) return 1;
          return 0;
        });
        setProfiles(sortedProfiles);
      } catch (err) {
        console.error("Error fetching profiles:", err);
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred"
        );
      } finally {
        setLoading(false);
      }
    };
    fetchProfiles();
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        setSelectedProfile(null);
      }
    };

    if (selectedProfile) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [selectedProfile]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setSelectedProfile(null);
      }
    };

    if (selectedProfile) {
      document.addEventListener("keydown", handleEscKey);
    }
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [selectedProfile]);

  const handleDeleteProfile = async (id: string) => {
    setDeletingId(id);
    setError(null);
    try {
      const response = await fetch(`/api/running-profiles/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete profile");
      }

      // If the deleted profile was the default or first one, we need to refresh
      const deletedProfile = profiles.find((p) => p.id === id);
      const isMainProfile = deletedProfile?.isDefault || profiles[0]?.id === id;

      if (isMainProfile && profiles.length > 1) {
        // Fetch profiles again to get the updated main profile
        const refreshResponse = await fetch("/api/running-profiles/list");
        if (refreshResponse.ok) {
          const data = await refreshResponse.json();
          setProfiles(data.runningProfiles);
        } else {
          // If refresh fails, just update the local state
          setProfiles(profiles.filter((profile) => profile.id !== id));
        }
      } else {
        // Just update the local state
        setProfiles(profiles.filter((profile) => profile.id !== id));
      }

      // Close modal if the deleted profile was selected
      if (selectedProfile?.id === id) {
        setSelectedProfile(null);
      }
    } catch (err) {
      console.error("Error deleting profile:", err);
      setError(err instanceof Error ? err.message : "Failed to delete profile");
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetDefault = async (id: string) => {
    setSettingDefaultId(id);
    setError(null);
    try {
      const profileToUpdate = profiles.find((p) => p.id === id);
      if (!profileToUpdate) return;

      const response = await fetch(`/api/running-profiles/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isDefault: true }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to set default profile");
      }

      // Fetch profiles again to get the updated data
      const refreshResponse = await fetch("/api/running-profiles/list");
      if (refreshResponse.ok) {
        const data = await refreshResponse.json();
        // Sort profiles to show default profile first
        const sortedProfiles = [...data.runningProfiles].sort((a, b) => {
          if (a.isDefault) return -1;
          if (b.isDefault) return 1;
          return 0;
        });
        setProfiles(sortedProfiles);
      } else {
        // If refresh fails, just update the local state
        setProfiles(
          profiles.map((profile) => ({
            ...profile,
            isDefault: profile.id === id,
          }))
        );
      }
    } catch (err) {
      console.error("Error setting default profile:", err);
      setError(
        err instanceof Error ? err.message : "Failed to set default profile"
      );
    } finally {
      setSettingDefaultId(null);
    }
  };

  const handleProfileClick = (profile: RunningProfile) => {
    if (isDesktop) {
      setSelectedProfile(profile);
    } else {
      router.push(`/running-profiles/${profile.id}`);
    }
  };

  // Format date as DD.MM.YYYY
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd.MM.yyyy");
  };

  // Handle slider navigation
  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
      if (sliderRef.current) {
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide - 1),
          behavior: "smooth",
        });
      }
    }
  };

  const handleNextSlide = () => {
    if (profiles.length > 0 && currentSlide < profiles.length) {
      setCurrentSlide(currentSlide + 1);
      if (sliderRef.current) {
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide + 1),
          behavior: "smooth",
        });
      }
    }
  };

  // Handle scroll events to update current slide
  useEffect(() => {
    const handleScroll = () => {
      if (sliderRef.current) {
        const scrollLeft = sliderRef.current.scrollLeft;
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        if (cardWidth > 0) {
          const newSlide = Math.round(scrollLeft / cardWidth);
          if (newSlide !== currentSlide) {
            setCurrentSlide(newSlide);
          }
        }
      }
    };

    const sliderElement = sliderRef.current;
    if (sliderElement && isMobile) {
      sliderElement.addEventListener("scroll", handleScroll);
      return () => {
        sliderElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, [currentSlide, isMobile]);

  return (
    <div className="space-y-6 pb-12">
      {error && (
        <Card className="bg-red-100 dark:bg-red-900/20 border border-red-500/50 dark:border-red-500/50 text-red-800 dark:text-red-300 text-xs font-mono">
          <CardContent className="p-3">{error}</CardContent>
        </Card>
      )}

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-10 w-10 text-brand-black/50 dark:text-text-main/50 animate-spin" />
        </div>
      ) : (
        <>
          {profiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg p-8 text-center bg-white dark:bg-zinc-800/30">
              <UserRound className="h-10 w-10 text-zinc-400 dark:text-zinc-500 mb-4" />
              <h3 className="text-lg font-medium text-zinc-800 dark:text-zinc-200 font-sans">
                No running profiles yet
              </h3>
              <p className="text-sm text-zinc-500 dark:text-zinc-400 mb-4 max-w-xs font-input">
                Create your first profile to unlock personalized shoe matches.
              </p>
              <Button
                className="bg-brand-green text-off-white hover:bg-brand-green-light font-sans text-sm h-9 group relative overflow-hidden transition-colors duration-300"
                asChild
              >
                <Link href="/create-eigen-profile">
                  <span className="relative z-10 flex items-center">
                    <PlusCircle className="mr-1.5 h-4 w-4" />
                    Create Profile
                  </span>
                  <span className="absolute inset-0 bg-brand-black/20 dark:bg-text-main/20 scale-0 transition-transform duration-200 group-hover:scale-100 origin-center"></span>
                </Link>
              </Button>
            </div>
          ) : (
            <>
              {/* Page Title */}
              <div className="flex items-center justify-between">
                <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground">
                  Running Profiles
                </h1>
                <Button
                  className="bg-primary text-primary-foreground hover:bg-primary/90 font-sans"
                  onClick={() => router.push("/create-eigen-profile")}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  New Profile
                </Button>
              </div>

              {/* Mobile Slider Navigation will be at the bottom */}

              {/* Profiles Grid/Slider */}
              <div
                ref={sliderRef}
                className={
                  isMobile
                    ? "flex overflow-x-auto snap-x snap-mandatory scrollbar-hide -mx-4 px-4 pb-4 gap-6"
                    : "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8"
                }
              >
                {profiles.map((profile, index) => {
                  const isDefault = profile.isDefault;
                  const isFirst = index === 0;
                  const showVideo =
                    isDefault ||
                    (isFirst && !profiles.some((p) => p.isDefault));

                  // Get recommendations (up to 5)
                  const recommendations = [];
                  if (profile.recommendation?.shoeModel1) {
                    recommendations.push({
                      name: profile.recommendation.shoeModel1.name,
                      id: profile.recommendation.shoeModel1.id,
                    });
                  }
                  if (profile.recommendation?.shoeModel2) {
                    recommendations.push({
                      name: profile.recommendation.shoeModel2.name,
                      id: profile.recommendation.shoeModel2.id,
                    });
                  }
                  if (profile.recommendation?.shoeModel3) {
                    recommendations.push({
                      name: profile.recommendation.shoeModel3.name,
                      id: profile.recommendation.shoeModel3.id,
                    });
                  }
                  if (profile.recommendation?.shoeModel4) {
                    recommendations.push({
                      name: profile.recommendation.shoeModel4.name,
                      id: profile.recommendation.shoeModel4.id,
                    });
                  }
                  if (profile.recommendation?.shoeModel5) {
                    recommendations.push({
                      name: profile.recommendation.shoeModel5.name,
                      id: profile.recommendation.shoeModel5.id,
                    });
                  }

                  return (
                    <motion.div
                      key={profile.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className={
                        isMobile ? "flex-shrink-0 w-[85vw] snap-center" : ""
                      }
                      style={{ maxHeight: "calc(85vh - 100px)" }}
                    >
                      <Card
                        className="overflow-hidden h-full flex flex-col bg-card hover:shadow-lg transition-all duration-300 cursor-pointer border-border hover:border-primary/50 group relative aspect-[2/3] rounded-2xl"
                        onClick={() => handleProfileClick(profile)}
                      >
                        {/* Background Media */}
                        <div className="absolute inset-0 w-full h-full">
                          {showVideo && profile.runningVideoSagittalUrl ? (
                            <div className="relative w-full h-full">
                              <StorageMedia
                                storagePath={profile.runningVideoSagittalUrl}
                                type="video"
                                aspectRatio="custom"
                                className="object-cover w-full h-full"
                                showFakeDataLabel={false}
                                loopPreview={true}
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
                            </div>
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40">
                              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
                            </div>
                          )}
                        </div>

                        {/* Content Overlay */}
                        <div className="relative z-10 flex flex-col h-full">
                          {/* Header */}
                          <div className="p-5 flex justify-between items-start">
                            <div>
                              <h3 className="font-sans text-xl font-semibold text-white mb-1">
                                {profile.name}
                              </h3>
                              <div className="flex items-center text-xs text-white/80">
                                <Calendar className="h-3 w-3 mr-1" />
                                {formatDate(profile.createdAt)}
                                {isDefault && (
                                  <span className="ml-2 inline-flex items-center rounded-full bg-white/20 px-2 py-0.5 text-xs font-medium text-white">
                                    <Star className="h-3 w-3 mr-1" />
                                    Default
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-1">
                              {!isDefault && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-white/70 hover:text-white hover:bg-white/10 transition-colors duration-200"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSetDefault(profile.id);
                                  }}
                                  disabled={settingDefaultId === profile.id}
                                >
                                  {settingDefaultId === profile.id ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Star className="h-4 w-4" />
                                  )}
                                </Button>
                              )}
                            </div>
                          </div>

                          {/* Main Content Area - Flex Column */}
                          <div className="flex flex-col flex-grow p-5 pt-0 pb-2">
                            {/* Profile Info */}
                            {profile.runningGoal && (
                              <div className="mb-3">
                                <p className="text-sm text-white/90">
                                  <span className="font-medium">Goal:</span>{" "}
                                  {profile.runningGoal}
                                </p>
                              </div>
                            )}

                            {/* Video Play Button (centered) */}
                            {showVideo && profile.runningVideoSagittalUrl && (
                              <div className="flex items-center justify-center flex-grow">
                                <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center">
                                  <PlayCircle className="h-8 w-8 text-white" />
                                </div>
                              </div>
                            )}

                            {/* If no video, add spacer */}
                            {(!showVideo ||
                              !profile.runningVideoSagittalUrl) && (
                              <div className="flex-grow"></div>
                            )}

                            {/* Recommendations Section - Positioned at bottom */}
                            {recommendations.length > 0 && (
                              <div className="mt-auto mb-0">
                                <h4 className="text-sm font-medium text-white/90 mb-2">
                                  Top Recommendations
                                </h4>
                                <div className="overflow-x-auto scrollbar-hide pb-1 -mx-2 px-2">
                                  <div className="flex gap-2 min-w-max">
                                    {recommendations.map((rec, idx) => (
                                      <Button
                                        key={rec.id}
                                        variant="ghost"
                                        className="text-xs px-4 py-3 h-auto rounded-lg bg-black/40 text-white/90 flex items-center hover:bg-black/50 hover:text-white backdrop-blur-sm border border-white/10 whitespace-nowrap"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          router.push(`/shoes/${rec.id}`);
                                        }}
                                      >
                                        <span className="text-white/80 mr-2 text-xs font-medium bg-white/10 rounded-full w-6 h-6 flex items-center justify-center">
                                          {idx + 1}
                                        </span>
                                        <span className="truncate max-w-[100px]">
                                          {rec.name}
                                        </span>
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  );
                })}

                {/* Add New Profile Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: profiles.length * 0.05 }}
                  className={
                    isMobile ? "flex-shrink-0 w-[85vw] snap-center" : ""
                  }
                  style={{ maxHeight: "calc(85vh - 100px)" }}
                >
                  <Card
                    className="h-full flex flex-col bg-[#23453b] hover:shadow-lg transition-all duration-300 cursor-pointer border-border hover:border-primary/50 group relative aspect-[2/3] rounded-2xl overflow-hidden"
                    onClick={() => router.push("/create-eigen-profile")}
                  >
                    {/* Background Gradient */}
                    <div className="absolute inset-0 w-full h-full">
                      <div className="w-full h-full bg-[#23453b]">
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent" />
                      </div>
                    </div>

                    {/* Content Overlay */}
                    <div className="relative z-10 flex flex-col h-full">
                      {/* Header */}
                      <div className="p-5">
                        <h3 className="font-sans text-xl font-semibold text-white mb-1">
                          Create New Profile
                        </h3>
                        <p className="text-sm text-white/80">
                          Add another runner or variation
                        </p>
                      </div>

                      {/* Middle Content - Flex Grow */}
                      <div className="flex-grow flex flex-col justify-center items-center p-4">
                        <div className="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center">
                          <PlusCircle className="h-10 w-10 text-white" />
                        </div>
                      </div>

                      {/* Bottom Content - No Button */}
                      <div className="p-5 pt-0"></div>
                    </div>
                  </Card>
                </motion.div>
              </div>

              {/* Mobile Navigation Arrows directly under cards */}
              {isMobile && profiles.length > 1 && (
                <div className="flex justify-center items-center mt-4 mb-6 gap-8 sticky bottom-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handlePrevSlide}
                    disabled={currentSlide === 0}
                    className="h-10 w-10 rounded-full bg-white shadow-md text-zinc-700 hover:bg-white"
                  >
                    <ArrowRight className="h-5 w-5 rotate-180" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleNextSlide}
                    disabled={currentSlide === profiles.length}
                    className="h-10 w-10 rounded-full bg-white shadow-md text-zinc-700 hover:bg-white"
                  >
                    <ArrowRight className="h-5 w-5" />
                  </Button>
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Desktop Modal for Profile Details */}
      <AnimatePresence>
        {isDesktop && selectedProfile && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 overflow-y-auto"
          >
            <motion.div
              ref={modalRef}
              initial={{ scale: 0.95, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.95, y: 20 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="bg-background rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto relative"
            >
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-10"
                onClick={() => setSelectedProfile(null)}
              >
                <X className="h-5 w-5" />
              </Button>

              <div className="p-6">
                <CreateRunningProfileProvider>
                  <div className="space-y-6 w-full max-w-full overflow-hidden">
                    {/* Profile Header */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-2xl font-sans font-semibold text-foreground">
                          {selectedProfile.name}
                        </h2>
                        <p className="text-sm text-muted-foreground">
                          Created on{" "}
                          {format(
                            new Date(selectedProfile.createdAt),
                            "MMMM d, yyyy"
                          )}
                        </p>
                      </div>
                      {selectedProfile.isDefault && (
                        <span className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                          <Star className="h-4 w-4 mr-1.5" />
                          Default Profile
                        </span>
                      )}
                    </div>

                    {/* Profile Detail View */}
                    <ProfileDetailView
                      profile={
                        {
                          ...selectedProfile,
                          // Ensure dates are properly formatted as strings
                          createdAt: selectedProfile.createdAt,
                          updatedAt: selectedProfile.updatedAt,
                        } as RunningProfile
                      }
                    />
                  </div>
                </CreateRunningProfileProvider>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
