"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  Star,
  Loader2,
  ShoppingBag,
  Award,
  Ruler,
  Scale,
  Droplets,
  Footprints,
  Thermometer,
  Layers,
  Dumbbell,
  Gauge,
  Zap,
  Flame,
  Mountain,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ShoeModel } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

// Animation variants
const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, ease: "easeOut" },
  },
};

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

interface RecommendingProfile {
  id: string;
  runningProfile: {
    id: string;
    name: string;
    userId: string;
  };
}

export default function ShoeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [shoe, setShoe] = useState<ShoeModel | null>(null);
  const [recommendingProfiles, setRecommendingProfiles] = useState<
    RecommendingProfile[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  useEffect(() => {
    const fetchShoeData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/shoe-models/${params.id}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch shoe data");
        }
        const data = await response.json();
        setShoe(data.shoeModel);
        setRecommendingProfiles(data.recommendedInProfiles || []);
      } catch (err) {
        console.error("Error fetching shoe data:", err);
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred"
        );
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchShoeData();
    }
  }, [params.id]);

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);
  };

  // Render rating stars
  const renderRating = (rating: number | undefined | null) => {
    if (rating === undefined || rating === null) return null;

    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < fullStars
                ? "text-yellow-400 fill-yellow-400"
                : i === fullStars && hasHalfStar
                ? "text-yellow-400 fill-yellow-400/50"
                : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          {rating.toFixed(1)} ({shoe?.numberOfReviews || 0} reviews)
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[70vh]">
        <Loader2 className="h-10 w-10 text-primary animate-spin" />
      </div>
    );
  }

  if (error || !shoe) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh] p-4">
        <h2 className="text-2xl font-sans font-semibold text-foreground mb-4">
          {error || "Shoe not found"}
        </h2>
        <Button onClick={() => router.back()} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      className="container max-w-6xl mx-auto py-8 px-4 sm:px-6"
      initial="hidden"
      animate="visible"
      variants={staggerVariant}
    >
      {/* Back button */}
      <motion.div variants={fadeInVariant} className="mb-6">
        <div className="flex items-center text-sm text-muted-foreground mb-2">
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto font-sans hover:bg-transparent hover:text-primary"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Running Profile
          </Button>
        </div>
      </motion.div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Left column - Images */}
        <motion.div variants={fadeInVariant} className="space-y-4">
          <div className="relative aspect-square bg-secondary rounded-xl overflow-hidden border border-border">
            {shoe.imageURLs && shoe.imageURLs.length > 0 ? (
              <Image
                src={shoe.imageURLs[activeImageIndex]}
                alt={shoe.fullName}
                fill
                className="object-contain p-4"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <Award className="h-24 w-24 text-muted-foreground/30" />
              </div>
            )}
          </div>

          {/* Thumbnail gallery */}
          {shoe.imageURLs && shoe.imageURLs.length > 1 && (
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
              {shoe.imageURLs.map((url, index) => (
                <button
                  key={index}
                  className={`relative h-20 w-20 rounded-md overflow-hidden border ${
                    activeImageIndex === index
                      ? "border-primary ring-2 ring-primary/20"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setActiveImageIndex(index)}
                >
                  <Image
                    src={url}
                    alt={`${shoe.fullName} view ${index + 1}`}
                    fill
                    className="object-contain p-1"
                  />
                </button>
              ))}
            </div>
          )}
        </motion.div>

        {/* Right column - Details */}
        <motion.div variants={fadeInVariant} className="space-y-6">
          <div>
            <div className="flex items-start justify-between">
              <div>
                <h1 className="text-3xl font-sans font-semibold text-foreground">
                  {shoe.fullName}
                </h1>
                <p className="text-muted-foreground">
                  {shoe.brand} · {shoe.modelYear}
                </p>
              </div>
              <Badge
                variant="outline"
                className="text-xs px-2 py-0 h-6 bg-primary/5 border-primary/20 text-primary"
              >
                {shoe.category}
              </Badge>
            </div>

            {/* Price and rating */}
            <div className="mt-4 flex items-center justify-between">
              <p className="text-2xl font-sans font-medium text-foreground">
                {formatPrice(shoe.price)}
              </p>
              {renderRating(shoe.rating)}
            </div>
          </div>

          <Separator />

          {/* Description */}
          <div>
            <h2 className="text-lg font-sans font-medium text-foreground mb-2">
              Description
            </h2>
            <p className="text-muted-foreground font-input">
              {shoe.description}
            </p>
          </div>

          {/* Pros and Cons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-foreground flex items-center">
                <span className="text-green-500 mr-1">+</span> Pros
              </h3>
              <ul className="space-y-1">
                {shoe.pros &&
                  shoe.pros.map((pro, index) => (
                    <li
                      key={index}
                      className="text-sm text-muted-foreground font-input flex items-start"
                    >
                      <span className="text-green-500 mr-1.5 mt-0.5">•</span>
                      {pro}
                    </li>
                  ))}
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-foreground flex items-center">
                <span className="text-red-500 mr-1">-</span> Cons
              </h3>
              <ul className="space-y-1">
                {shoe.cons &&
                  shoe.cons.map((con, index) => (
                    <li
                      key={index}
                      className="text-sm text-muted-foreground font-input flex items-start"
                    >
                      <span className="text-red-500 mr-1.5 mt-0.5">•</span>
                      {con}
                    </li>
                  ))}
              </ul>
            </div>
          </div>

          {/* Buy button */}
          <div className="pt-4">
            <Button className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-sans">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Buy Now
            </Button>
          </div>

          {/* Recommended in profiles */}
          {recommendingProfiles.length > 0 && (
            <div className="pt-2">
              <h3 className="text-sm font-medium text-foreground mb-2 flex items-center">
                <Award className="h-4 w-4 mr-1.5 text-primary" />
                Recommended in your profiles
              </h3>
              <div className="flex flex-wrap gap-2">
                {recommendingProfiles.map((rec) => (
                  <Link
                    key={rec.id}
                    href={`/running-profiles/${rec.runningProfile.id}`}
                    className="text-xs px-3 py-1.5 rounded-full bg-primary/10 text-primary hover:bg-primary/20 transition-colors"
                  >
                    {rec.runningProfile.name}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Technical specifications tabs */}
      <motion.div variants={fadeInVariant} className="mt-8">
        <Tabs defaultValue="specs" className="w-full">
          <TabsList className="w-full max-w-md mx-auto grid grid-cols-2 h-auto p-1 mb-6">
            <TabsTrigger value="specs" className="py-2 font-sans">
              Technical Specs
            </TabsTrigger>
            <TabsTrigger value="fit" className="py-2 font-sans">
              Fit & Feel
            </TabsTrigger>
          </TabsList>

          <TabsContent value="specs" className="mt-0">
            <Card className="border-border/50">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Stack Height & Drop */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Ruler className="h-4 w-4 mr-1.5 text-primary/70" />
                      Stack Height & Drop
                    </h3>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-secondary/50 p-3 rounded-lg">
                        <p className="text-xs text-muted-foreground">
                          Heel Stack
                        </p>
                        <p className="text-lg font-medium">
                          {shoe.heelStackHeight || "-"} mm
                        </p>
                      </div>
                      <div className="bg-secondary/50 p-3 rounded-lg">
                        <p className="text-xs text-muted-foreground">Drop</p>
                        <p className="text-lg font-medium">
                          {shoe.drop || "-"} mm
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Weight */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Scale className="h-4 w-4 mr-1.5 text-primary/70" />
                      Weight
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Weight</p>
                      <p className="text-lg font-medium">
                        {shoe.shoeWeightG || "-"} g
                      </p>
                    </div>
                  </div>

                  {/* Midsole */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Layers className="h-4 w-4 mr-1.5 text-primary/70" />
                      Midsole
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Material</p>
                      <p className="text-lg font-medium">
                        {shoe.midsoleFormCategory || "-"}
                      </p>
                    </div>
                  </div>

                  {/* Outsole */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Footprints className="h-4 w-4 mr-1.5 text-primary/70" />
                      Outsole
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Thickness</p>
                      <p className="text-lg font-medium">
                        {shoe.outsoleThickness || "-"} mm
                      </p>
                    </div>
                  </div>

                  {/* Terrain */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Mountain className="h-4 w-4 mr-1.5 text-primary/70" />
                      Terrain
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Best For</p>
                      <p className="text-lg font-medium">
                        {shoe.terrain || "-"}
                      </p>
                    </div>
                  </div>

                  {/* Type */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Zap className="h-4 w-4 mr-1.5 text-primary/70" />
                      Type
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Category</p>
                      <p className="text-lg font-medium">{shoe.type || "-"}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fit" className="mt-0">
            <Card className="border-border/50">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Softness */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Droplets className="h-4 w-4 mr-1.5 text-primary/70" />
                      Softness
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">
                        Midsole Softness (1-10)
                      </p>
                      <div className="w-full bg-secondary rounded-full h-2 mt-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(shoe.midsoleSoftness || 0) * 10}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-right text-xs mt-1">
                        {shoe.midsoleSoftness || "-"}/10
                      </p>
                    </div>
                  </div>

                  {/* Cold Weather Performance */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Thermometer className="h-4 w-4 mr-1.5 text-primary/70" />
                      Cold Weather
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">
                        Softness in Cold (1-10)
                      </p>
                      <div className="w-full bg-secondary rounded-full h-2 mt-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(shoe.midsoleSoftnessInCold || 0) * 10}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-right text-xs mt-1">
                        {shoe.midsoleSoftnessInCold || "-"}/10
                      </p>
                    </div>
                  </div>

                  {/* Flexibility */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Flame className="h-4 w-4 mr-1.5 text-primary/70" />
                      Flexibility
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">
                        Forefoot Flexibility (1-10)
                      </p>
                      <div className="w-full bg-secondary rounded-full h-2 mt-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(shoe.forefootFlexibility || 0) * 10}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-right text-xs mt-1">
                        {shoe.forefootFlexibility || "-"}/10
                      </p>
                    </div>
                  </div>

                  {/* Toebox */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Award className="h-4 w-4 mr-1.5 text-primary/70" />
                      Toebox
                    </h3>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-secondary/50 p-3 rounded-lg">
                        <p className="text-xs text-muted-foreground">Width</p>
                        <p className="text-lg font-medium">
                          {shoe.toeboxWidth || "-"}
                        </p>
                      </div>
                      <div className="bg-secondary/50 p-3 rounded-lg">
                        <p className="text-xs text-muted-foreground">Height</p>
                        <p className="text-lg font-medium">
                          {shoe.toeboxHeight || "-"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Stability */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Dumbbell className="h-4 w-4 mr-1.5 text-primary/70" />
                      Stability
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">Type</p>
                      <p className="text-lg font-medium">
                        {shoe.stability || "-"}
                      </p>
                    </div>
                  </div>

                  {/* Torsional Rigidity */}
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                      <Gauge className="h-4 w-4 mr-1.5 text-primary/70" />
                      Torsional Rigidity
                    </h3>
                    <div className="bg-secondary/50 p-3 rounded-lg">
                      <p className="text-xs text-muted-foreground">
                        Rigidity (1-10)
                      </p>
                      <div className="w-full bg-secondary rounded-full h-2 mt-2">
                        <div
                          className="bg-primary h-2 rounded-full"
                          style={{
                            width: `${(shoe.torsionalRigidity || 0) * 10}%`,
                          }}
                        ></div>
                      </div>
                      <p className="text-right text-xs mt-1">
                        {shoe.torsionalRigidity || "-"}/10
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  );
}
