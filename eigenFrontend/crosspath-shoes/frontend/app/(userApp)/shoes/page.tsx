"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ShoeModel } from "@/components/shoe-model";
import { mockShoes } from "@/lib/mock-data";
import { MileageTracker } from "@/components/mileage-tracker";
import { AlertCircle, ArrowRight, Info } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

// Utility functions for size conversion
const euToUs = (eu: number) => ((eu - 31.5) * 2) / 2;
const euToUk = (eu: number) => ((eu - 31.5) * 2 - 1) / 2;

export default function ShoesPage() {
  return (
    <div className="space-y-6">
      {/* Title removed, now handled in Header */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {mockShoes.map((shoe) => (
          <Card
            key={shoe.id}
            className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg flex flex-col overflow-hidden"
          >
            <CardHeader className="p-4 border-b border-zinc-200 dark:border-zinc-700">
              <div className="flex items-center justify-between gap-2">
                <CardTitle className="font-sans text-base font-medium text-zinc-900 dark:text-zinc-100 leading-tight">
                  {shoe.name}
                </CardTitle>
                <Badge
                  variant={
                    shoe.status === "active"
                      ? "default"
                      : shoe.status === "ordered"
                      ? "secondary"
                      : "outline"
                  }
                  className={`text-[10px] font-mono px-1.5 py-0.5 leading-tight whitespace-nowrap mt-px rounded-sm border ${
                    shoe.status === "active"
                      ? "bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/50 dark:text-emerald-200 dark:border-emerald-700/70"
                      : shoe.status === "ordered"
                      ? "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-700/70"
                      : "bg-zinc-100 text-zinc-600 border-zinc-200 dark:bg-zinc-700 dark:text-zinc-400 dark:border-zinc-600"
                  }`}
                >
                  {shoe.status === "active"
                    ? "Active"
                    : shoe.status === "ordered"
                    ? "Ordered"
                    : "Retired"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-4 space-y-4 flex-grow">
              <div className="h-[180px] bg-white dark:bg-zinc-700/50 rounded-md flex items-center justify-center border border-zinc-200 dark:border-zinc-700">
                <ShoeModel
                  modelPath={"/models/default-shoe.glb"}
                  color={shoe.customizations?.color}
                  className="h-full w-full cursor-grab active:cursor-grabbing"
                />
              </div>
              <div className="space-y-1.5 font-input text-xs">
                <div className="flex justify-between">
                  <span className="text-zinc-500 dark:text-zinc-400">
                    Size:
                  </span>
                  <span className="text-zinc-700 dark:text-zinc-300 font-medium font-jetbrains">
                    EU {shoe.customizations?.size || "-"} / US{" "}
                    {shoe.customizations?.size
                      ? euToUs(shoe.customizations.size).toFixed(1)
                      : "-"}{" "}
                    / UK{" "}
                    {shoe.customizations?.size
                      ? euToUk(shoe.customizations.size).toFixed(1)
                      : "-"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-500 dark:text-zinc-400">
                    Width:
                  </span>
                  <span className="text-zinc-700 dark:text-zinc-300 font-medium font-jetbrains capitalize">
                    {shoe.customizations?.width || "-"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-500 dark:text-zinc-400">
                    Cushioning:
                  </span>
                  <span className="text-zinc-700 dark:text-zinc-300 font-medium font-jetbrains capitalize">
                    {shoe.customizations?.cushioning || "-"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-500 dark:text-zinc-400">
                    Purchase Date:
                  </span>
                  <span className="text-zinc-700 dark:text-zinc-300 font-medium font-jetbrains">
                    {new Date(shoe.purchaseDate).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {shoe.status !== "ordered" && (
                <div className="pt-2">
                  <MileageTracker
                    currentMileage={shoe.totalMileage}
                    maxMileage={shoe.maxMileage}
                  />
                  <p className="text-[10px] text-zinc-500 dark:text-zinc-400 mt-1.5 text-center">
                    Rec. Replacement: {shoe.maxMileage} km
                  </p>
                </div>
              )}
            </CardContent>

            <CardFooter className="border-t border-zinc-200 dark:border-zinc-700 p-3 mt-auto bg-zinc-100 dark:bg-zinc-800/80">
              {shoe.status === "active" &&
                shoe.totalMileage > shoe.maxMileage * 0.8 && (
                  <Alert
                    variant="destructive"
                    className="bg-red-100 border-red-300 text-red-700 dark:bg-red-900/30 dark:border-red-600/50 dark:text-red-400 w-full p-2.5 rounded-md"
                  >
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle className="text-xs font-medium">
                      Replace Soon
                    </AlertTitle>
                    <AlertDescription className="text-xs leading-tight">
                      Near recommended lifespan.
                    </AlertDescription>
                  </Alert>
                )}
              {shoe.status === "ordered" && (
                <Button
                  className="w-full bg-brand-black text-off-white dark:bg-off-white dark:text-brand-black hover:bg-brand-black/90 dark:hover:bg-off-white/90 font-sans text-sm h-8 group relative overflow-hidden transition-colors duration-300"
                  asChild
                >
                  <Link href="/orders">
                    <span className="relative z-10 flex items-center justify-center">
                      Track Order
                      <ArrowRight className="ml-1.5 h-4 w-4" />
                    </span>
                    <span className="absolute inset-0 bg-brand-black/20 dark:bg-brand-black/20 scale-0 transition-transform duration-200 group-hover:scale-100 origin-center"></span>
                  </Link>
                </Button>
              )}
              {shoe.status === "retired" && (
                <div className="text-xs text-zinc-500 dark:text-zinc-400 font-input text-center w-full italic">
                  Retired
                </div>
              )}
              {shoe.status === "active" &&
                shoe.totalMileage <= shoe.maxMileage * 0.8 && (
                  <div className="flex items-center justify-center text-xs text-zinc-500 dark:text-zinc-400 font-input w-full gap-1">
                    <Info className="h-3.5 w-3.5" /> Good to go!
                  </div>
                )}
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="flex justify-center pt-4">
        <Button
          size="lg"
          className="bg-brand-green text-off-white hover:bg-brand-green-light font-sans h-10 px-6 group relative overflow-hidden transition-colors duration-300"
          asChild
        >
          <Link href="/customize">
            <span className="relative z-10 flex items-center justify-center">
              Design New Pair
              <ArrowRight className="ml-2 h-4 w-4" />
            </span>
            {/* <span className="absolute inset-0 bg-brand-green-light/20 scale-0 transition-transform duration-200 group-hover:scale-100 origin-center"></span> */}
          </Link>
        </Button>
      </div>
    </div>
  );
}
