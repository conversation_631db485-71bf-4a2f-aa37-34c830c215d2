"use client"

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { mockOrders } from "@/lib/mock-data"
import { Package, Truck, CheckCircle2, Clock } from "lucide-react"
import { cn } from "@/lib/utils"

const statusIcons = {
  pending: Clock,
  in_production: Package,
  shipped: Truck,
  delivered: CheckCircle2
}

const statusColors = {
  pending: "text-yellow-500",
  in_production: "text-blue-500",
  shipped: "text-purple-500",
  delivered: "text-green-500"
}

export default function OrdersPage() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-primary">My Orders</h1>
        <p className="text-muted-foreground mt-2">
          Track your orders and view order history.
        </p>
      </div>

      <div className="grid gap-6">
        {mockOrders.map(order => {
          const StatusIcon = statusIcons[order.status]
          return (
            <Card key={order.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{order.shoeName}</CardTitle>
                    <CardDescription>Order #{order.id}</CardDescription>
                  </div>
                  <div className={cn("flex items-center gap-2", statusColors[order.status])}>
                    <StatusIcon className="h-5 w-5" />
                    <span className="font-medium capitalize">{order.status.replace("_", " ")}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Order Date</span>
                      <span>{new Date(order.orderDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Est. Delivery</span>
                      <span>{new Date(order.estimatedDelivery).toLocaleDateString()}</span>
                    </div>
                    {order.trackingNumber && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tracking Number</span>
                        <span className="font-mono">{order.trackingNumber}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Price</span>
                      <span>${order.price.toFixed(2)}</span>
                    </div>
                  </div>
                  {order.status === "shipped" && (
                    <div className="rounded-lg border p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Truck className="h-4 w-4 text-primary" />
                        <span className="font-medium">Shipping Updates</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Your order is on its way! Expected delivery by {new Date(order.estimatedDelivery).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
                <div className="h-[200px] flex items-center justify-center bg-muted rounded-lg">
                  <span className="text-muted-foreground">Order Preview</span>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  View Order Details
                </Button>
              </CardFooter>
            </Card>
          )
        })}
      </div>
    </div>
  )
} 