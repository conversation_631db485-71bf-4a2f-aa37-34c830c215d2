import type React from "react";
import { DashboardHeader } from "@/components/dashboard-header"; // Header component
// import { DashboardSidebar } from "@/components/dashboard-sidebar" // Sidebar component - REMOVED
// import { BottomNavBar } from "@/components/bottom-nav-bar" // Import the new BottomNavBar - REMOVED

// This layout wraps all routes inside the (userApp) route group
export default function UserAppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    // Keep flex-col for Header on top
    <div className="flex flex-col h-screen font-mono bg-zinc-50 text-brand-black overflow-hidden">
      {" "}
      {/* Set fixed screen height and hide main overflow */}
      {/* Header remains sticky/fixed implicitly by layout */}
      <DashboardHeader />
      {/* Container for Content (takes remaining height) */}
      <div className="flex flex-1 overflow-hidden">
        {" "}
        {/* flex-1 takes remaining height, overflow-hidden prevents body scroll */}
        {/* Sidebar - REMOVED */}
        {/* <DashboardSidebar /> */}
        {/* Main content area - Now handles its own scrolling explicitly */}
        {/* Removed padding-bottom for BottomNavBar, keep other padding */}
        <main className="flex-1 overflow-y-auto bg-zinc-100/50 pt-3 px-6 pb-6 sm:p-8 md:p-10">
          {children} {/* The specific page content will be rendered here */}
        </main>
      </div>
      {/* Render BottomNavBar - REMOVED */}
      {/* <BottomNavBar /> */}
    </div>
  );
}
