"use client";

import React, { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import {
  updateRunningProfileDetails,
  getRunningProfileData,
} from "@/actions/running-profile";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { ArrowRight, ArrowLeft, Loader2, User as UserIcon } from "lucide-react";

// Helper function to parse injury string
const parseInjuries = (injuryString: string): string[] => {
  if (!injuryString) return [];
  return injuryString
    .split(",")
    .map((s) => s.trim())
    .filter(Boolean);
};

export default function RunnerProfilePage() {
  const { profileId, profileData, steps, setCurrentStep } = useRunningProfile();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Form State - Only include fields relevant for user input here
  const [age, setAge] = useState<string>(profileData.age?.toString() ?? "");
  const [heightCm, setHeightCm] = useState<string>(
    profileData.heightCm?.toString() ?? ""
  );
  const [weightKg, setWeightKg] = useState<string>(
    profileData.weightKg?.toString() ?? ""
  );
  const [gender, setGender] = useState<string>(profileData.gender ?? "");
  const [climate, setClimate] = useState<string>(profileData.climate ?? ""); // Keep?
  const [terrain, setTerrain] = useState<string>(profileData.terrain ?? "");
  const [runningGoal, setRunningGoal] = useState<string>(
    profileData.runningGoal ?? ""
  );
  const [avgKm, setAvgKm] = useState<string>(
    profileData.averageWeeklyKm?.toString() ?? ""
  );
  // Pace and Cadence might be derivable, but let's keep as optional context for now
  const [avgPace, setAvgPace] = useState<string>(
    profileData.averagePaceEasyLong ?? ""
  );
  // const [avgCadence, setAvgCadence] = useState<string>(profileData.averageCadence?.toString() ?? '');
  const [previousInjuries, setPreviousInjuries] = useState<string>(
    profileData.previousInjuries || ""
  );

  // Find next/prev steps - Next step should now be the 'Connect Wearables' placeholder or trigger analysis?
  const currentStepIndex = steps.findIndex((step) =>
    step.href.includes("runner-profile")
  );
  const nextStep = steps[currentStepIndex + 1]; // Adjust this based on new flow
  const prevStep = steps[currentStepIndex - 1];

  // Fetch latest data from the database when the component mounts
  useEffect(() => {
    if (profileId) {
      startTransition(async () => {
        try {
          const { profile, error } = await getRunningProfileData(profileId);

          if (error) {
            console.error("Error fetching profile data:", error);
            setError(error);
            return;
          }

          if (profile) {
            // Update the context with the latest data
            console.log("Fetched latest profile data:", profile);

            // Update form state with the latest data
            setAge(profile.age?.toString() ?? "");
            setHeightCm(profile.heightCm?.toString() ?? "");
            setWeightKg(profile.weightKg?.toString() ?? "");
            setGender(profile.gender ?? "");
            setClimate(profile.climate ?? "");
            setTerrain(profile.terrain ?? "");
            setRunningGoal(profile.runningGoal ?? "");
            setAvgKm(profile.averageWeeklyKm?.toString() ?? "");
            setAvgPace(profile.averagePaceEasyLong ?? "");
            setPreviousInjuries(profile.previousInjuries || "");
          }
        } catch (err) {
          console.error("Error fetching profile data:", err);
          setError(
            err instanceof Error ? err.message : "Failed to fetch profile data"
          );
        }
      });
    }
  }, [profileId, startTransition]);

  // Sync state if profileData changes
  useEffect(() => {
    setAge(profileData.age?.toString() ?? "");
    setHeightCm(profileData.heightCm?.toString() ?? "");
    setWeightKg(profileData.weightKg?.toString() ?? "");
    setGender(profileData.gender ?? "");
    setClimate(profileData.climate ?? "");
    setTerrain(profileData.terrain ?? "");
    setRunningGoal(profileData.runningGoal ?? "");
    setAvgKm(profileData.averageWeeklyKm?.toString() ?? "");
    setAvgPace(profileData.averagePaceEasyLong ?? "");
    // setAvgCadence(profileData.averageCadence?.toString() ?? '');
    setPreviousInjuries(profileData.previousInjuries || "");
  }, [profileData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!profileId) {
      setError("Profile ID is missing. Cannot save data.");
      toast.error("Error: Profile not found. Please go back and start again.");
      return;
    }
    setError(null);

    // Only include fields collected in *this* form
    const dataToUpdate = {
      age: age ? parseInt(age, 10) : null,
      heightCm: heightCm ? parseFloat(heightCm) : null,
      weightKg: weightKg ? parseFloat(weightKg) : null,
      gender: gender || null,
      climate: climate || null,
      terrain: terrain || null,
      runningGoal: runningGoal || null,
      averageWeeklyKm: avgKm || null,
      averagePaceEasyLong: avgPace || null,
      // averageCadence: avgCadence ? parseInt(avgCadence, 10) : null,
      previousInjuries: previousInjuries || null,
    };

    startTransition(async () => {
      try {
        // Use the same update action, but it now only sends relevant fields
        const result = await updateRunningProfileDetails(
          profileId,
          dataToUpdate
        );
        if (result.error) {
          throw new Error(result.error);
        }
        if (result.success) {
          toast.success("Runner profile details saved!");
          setCurrentStep(currentStepIndex + 1);
          // Navigate to the next logical step (e.g., Wearables placeholder)
          router.push(nextStep?.href || "/");
        }
      } catch (err) {
        const message =
          err instanceof Error ? err.message : "An unexpected error occurred.";
        setError(`Save failed: ${message}`);
        toast.error(`Save failed: ${message}`);
      }
    });
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(prevStep?.href || "/");
  };

  return (
    <div className="space-y-6 pb-12">
      <Card className="border-border/50 overflow-hidden">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-sans text-xl font-semibold text-foreground">
              <UserIcon className="h-5 w-5 text-primary" /> Runner Information
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Please provide some background information about yourself and your
              running. This helps contextualize the analysis for better
              recommendations.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {error && (
              <div className="p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {error}
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="age"
                  className="text-sm font-medium text-foreground"
                >
                  Age
                </Label>
                <Input
                  id="age"
                  type="number"
                  placeholder="e.g., 30"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                  disabled={isPending}
                  className="border-border focus:border-primary"
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="height"
                  className="text-sm font-medium text-foreground"
                >
                  Height (cm)
                </Label>
                <Input
                  id="height"
                  type="number"
                  step="any"
                  placeholder="e.g., 175"
                  value={heightCm}
                  onChange={(e) => setHeightCm(e.target.value)}
                  disabled={isPending}
                  className="border-border focus:border-primary"
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="weight"
                  className="text-sm font-medium text-foreground"
                >
                  Weight (kg)
                </Label>
                <Input
                  id="weight"
                  type="number"
                  step="any"
                  placeholder="e.g., 70"
                  value={weightKg}
                  onChange={(e) => setWeightKg(e.target.value)}
                  disabled={isPending}
                  className="border-border focus:border-primary"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="gender"
                  className="text-sm font-medium text-foreground"
                >
                  Gender (Optional)
                </Label>
                <Select
                  value={gender}
                  onValueChange={setGender}
                  disabled={isPending}
                >
                  <SelectTrigger id="gender" className="border-border focus:border-primary">
                    <SelectValue placeholder="Select Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Male">
                      Male
                    </SelectItem>
                    <SelectItem value="Female">
                      Female
                    </SelectItem>
                    <SelectItem value="Other">
                      Other
                    </SelectItem>
                    <SelectItem value="Prefer not to say">
                      Prefer not to say
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="runningGoal"
                  className="text-sm font-medium text-foreground"
                >
                  Running Goal
                </Label>
                <Select
                  value={runningGoal}
                  onValueChange={setRunningGoal}
                  disabled={isPending}
                >
                  <SelectTrigger id="runningGoal" className="border-border focus:border-primary">
                    <SelectValue placeholder="Select Goal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Training">
                      Training
                    </SelectItem>
                    <SelectItem value="Racing">
                      Racing
                    </SelectItem>
                    <SelectItem value="Recreational">
                      Recreational
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="climate"
                  className="text-sm font-medium text-foreground"
                >
                  Typical Climate (Optional)
                </Label>
                <Select
                  value={climate}
                  onValueChange={setClimate}
                  disabled={isPending}
                >
                  <SelectTrigger id="climate" className="border-border focus:border-primary">
                    <SelectValue placeholder="Select Climate" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Hot">
                      Hot / Humid
                    </SelectItem>
                    <SelectItem value="Temperate">
                      Temperate
                    </SelectItem>
                    <SelectItem value="Cold">
                      Cold / Dry
                    </SelectItem>
                    <SelectItem value="Mixed">
                      Mixed
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="terrain"
                  className="text-sm font-medium text-foreground"
                >
                  Primary Terrain
                </Label>
                <Select
                  value={terrain}
                  onValueChange={setTerrain}
                  disabled={isPending}
                >
                  <SelectTrigger id="terrain" className="border-border focus:border-primary">
                    <SelectValue placeholder="Select Terrain" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Road">
                      Road / Pavement
                    </SelectItem>
                    <SelectItem value="Track">
                      Track
                    </SelectItem>
                    <SelectItem value="Trail">
                      Trail (Technical/Varied)
                    </SelectItem>
                    <SelectItem value="Gravel">
                      Gravel / Light Trail
                    </SelectItem>
                    <SelectItem value="Treadmill">
                      Treadmill
                    </SelectItem>
                    <SelectItem value="Mixed">
                      Mixed
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="avgKm"
                  className="text-sm font-medium text-foreground"
                >
                  Avg. Weekly Km
                </Label>
                <Input
                  id="avgKm"
                  type="number"
                  step="any"
                  placeholder="e.g., 40"
                  value={avgKm}
                  onChange={(e) => setAvgKm(e.target.value)}
                  disabled={isPending}
                  className="border-border focus:border-primary"
                />
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="avgPace"
                  className="text-sm font-medium text-foreground"
                >
                  Avg. Easy Pace (min/km) (Optional)
                </Label>
                <Input
                  id="avgPace"
                  type="text"
                  placeholder="e.g., 5:30"
                  value={avgPace}
                  onChange={(e) => setAvgPace(e.target.value)}
                  disabled={isPending}
                  className="border-border focus:border-primary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="injuries"
                className="text-sm font-medium text-foreground"
              >
                Previous Injuries (comma-separated)
              </Label>
              <Textarea
                id="injuries"
                placeholder="e.g., Plantar fasciitis, Shin splints, IT band syndrome"
                value={previousInjuries}
                onChange={(e) => setPreviousInjuries(e.target.value)}
                rows={3}
                disabled={isPending}
                className="border-border focus:border-primary"
              />
              <p className="text-xs text-muted-foreground">
                List any significant past running-related injuries. Crucial for
                recommendations.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={isPending}
              className="border-border hover:bg-secondary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {isPending ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </span>
              ) : (
                <span className="flex items-center justify-center">
                  Save & Continue <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
