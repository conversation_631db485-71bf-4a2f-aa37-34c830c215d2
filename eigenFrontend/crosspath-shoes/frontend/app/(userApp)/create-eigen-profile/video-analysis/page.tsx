"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { VideoAnalysisStep } from '@/components/create-running-profile/VideoAnalysisStep';

export default function VideoAnalysisPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('video-analysis'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <VideoAnalysisStep
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}
