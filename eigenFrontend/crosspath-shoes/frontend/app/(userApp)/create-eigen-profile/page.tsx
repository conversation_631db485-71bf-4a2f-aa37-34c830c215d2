"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Footprints, Info } from "lucide-react";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { motion } from "framer-motion";
import { createOrUpdateRunningProfile } from "@/actions/running-profile";
import { toast } from "sonner";

export default function RunningProfileStartPage() {
  const router = useRouter();
  const { steps, setCurrentStep, setProfileId } = useRunningProfile();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStart = async () => {
    setIsSubmitting(true);
    setError(null);

    const result = await createOrUpdateRunningProfile("New Running Profile");

    if (result.error || !result.profileId) {
      const errorMessage =
        result.error || "Failed to create profile. Please try again.";
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
      setIsSubmitting(false);
      return;
    }

    setProfileId(result.profileId);

    const firstStepIndex = steps.findIndex((step) =>
      step.href.includes("foot-imaging/top-left")
    );
    const firstStepHref = steps[firstStepIndex]?.href;

    if (firstStepHref) {
      if (firstStepIndex !== -1) {
        setCurrentStep(firstStepIndex);
      }
      toast.success("Let's start your profile!");
      router.push(firstStepHref);
    } else {
      toast.error(
        "Could not determine the first step. Please contact support."
      );
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pb-12">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground">
          Create Running Profile
        </h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl mx-auto"
      >
        {/* Welcome Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center p-4 bg-primary/10 rounded-full mb-6">
            <Footprints className="h-8 w-8 text-primary" />
          </div>
          <h2 className="font-sans text-3xl font-semibold text-foreground mb-4">
            Your Perfect Running Shoes Await
          </h2>
          <p className="text-lg text-muted-foreground leading-relaxed mb-8 max-w-xl mx-auto">
            Create your running profile to get personalized shoe recommendations
            backed by AI analysis. We'll analyze your foot structure and running
            style to find your perfect match.
          </p>

          {/* Start Button */}
          <Button
            onClick={handleStart}
            className="bg-primary text-primary-foreground hover:bg-primary/90 font-sans font-medium transition-colors duration-300 px-8 py-3 text-lg"
            size="lg"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center gap-2">
                <svg
                  className="animate-spin h-5 w-5 text-inherit"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Starting...
              </span>
            ) : (
              <span className="flex items-center justify-center gap-2">
                Let's Begin <ArrowRight className="h-5 w-5" />
              </span>
            )}
          </Button>
        </div>

        {/* Process Steps Card */}
        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="font-sans text-xl font-semibold text-foreground">
              Running Profile Creation Process
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Follow these steps to complete your running profile:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  title: "Foot Photos",
                  desc: "Upload top-down and medial photos of both feet.",
                  icon: "📸",
                },
                {
                  title: "Running Videos",
                  desc: "Upload short posterior (back) and sagittal (side) view videos.",
                  icon: "🎥",
                },
                {
                  title: "Runner Profile",
                  desc: "Provide details about yourself and any injury history.",
                  icon: "👤",
                },
                {
                  title: "AI Analysis",
                  desc: "Our AI analyzes your data to generate insights.",
                  icon: "🤖",
                },
                {
                  title: "Review & Save",
                  desc: "Confirm your details and get recommendations!",
                  icon: "✅",
                },
              ].map((step, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-secondary/30 rounded-lg">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary text-sm font-medium mt-0.5">
                    {step.icon}
                  </div>
                  <div className="flex-1">
                    <p className="font-sans font-medium text-foreground text-sm">
                      {step.title}
                    </p>
                    <p className="text-xs text-muted-foreground leading-snug mt-0.5">
                      {step.desc}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Info Alert */}
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed">
                  The more accurate your photos and information, the better our
                  AI can analyze your feet and running style to recommend the
                  perfect shoes for you.
                </p>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {error}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
