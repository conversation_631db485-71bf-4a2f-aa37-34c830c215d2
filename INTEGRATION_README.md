# Video Analysis Backend Integration

This document describes the integration between the video analysis backend (eigenPose) and the frontend application (eigenFrontend) for automated gait analysis.

## Overview

The integration enables users to upload running videos through the frontend, which are then automatically processed by the backend using OpenPose for biomechanical analysis. Results are stored in the database and displayed in the user's profile.

## Architecture

```
Frontend (Next.js) → API Routes → Backend (FastAPI) → OpenPose → Database (PostgreSQL)
```

### Components

1. **Frontend**: Next.js application with video upload and profile management
2. **Backend**: FastAPI application with OpenPose integration for video analysis
3. **Database**: PostgreSQL database with Prisma ORM for data persistence
4. **Storage**: Supabase storage for video and image files

## Setup Instructions

### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd eigenPose/eigenComputePose
   ```

2. **Copy environment configuration**:
   ```bash
   cp .env.example .env
   ```

3. **Configure environment variables** in `.env`:
   ```bash
   # Database Configuration (use same as frontend)
   DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
   DIRECT_URL="postgresql://username:password@localhost:5432/database_name"
   
   # API Configuration
   BACKEND_API_HOST=0.0.0.0
   BACKEND_API_PORT=8000
   ```

4. **Start the backend**:
   ```bash
   ./start_backend.sh
   ```

   Or manually:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   uvicorn api.main:app --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd eigenFrontend/crosspath-shoes/frontend
   ```

2. **Configure environment variables** in `.env.local`:
   ```bash
   # Backend API Configuration
   BACKEND_API_URL="http://localhost:8000"
   
   # Database Configuration
   DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
   DIRECT_URL="postgresql://username:password@localhost:5432/database_name"
   
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
   NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
   SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
   ```

3. **Install dependencies and start**:
   ```bash
   npm install
   npm run dev
   ```

## API Endpoints

### Backend Endpoints

- `POST /analyze-url`: Analyze video from URL with database integration
- `POST /analyze`: Analyze local video file
- `GET /health`: Health check endpoint
- `GET /config`: Get backend configuration

### Frontend API Routes

- `POST /api/analyze-video`: Trigger video analysis for a profile
- `POST /api/openpose-process`: Process video with OpenPose (legacy)

## Workflow

1. **Video Upload**: User uploads sagittal running video through frontend
2. **Storage**: Video is stored in Supabase storage
3. **Analysis Trigger**: Frontend calls `/api/analyze-video` with video URL and profile ID
4. **Backend Processing**: 
   - Downloads video from signed URL
   - Processes with OpenPose
   - Extracts biomechanical measurements
   - Updates database with results
   - Cleans up temporary files
5. **Results Display**: Frontend shows analysis results in user profile

## Database Schema

The analysis results are stored in the `RunningProfile` table with these fields:

- `pelvicDrop`: Pelvic drop analysis
- `kneeDrift`: Knee valgus/varus measurement
- `footDrift`: Foot crossover detection
- `heelWhip`: Medial/lateral heel movement
- `posteriorStabilityScore`: Composite stability score
- `overstride`: Heel-to-hip distance at contact
- `ankleDorsiflexion`: Foot strike classification
- `anklePlantarflexion`: Propulsive power measurement
- `verticalOscillationVideo`: Vertical oscillation
- `trunkLean`: Trunk lean angle
- `kneeFlexionLoading`: Knee flexion during loading

## User Experience

1. **Profile Creation**: Users follow guided steps to create their running profile
2. **Video Upload**: Upload sagittal (side view) running video
3. **Analysis Step**: Automatic analysis with progress indication
4. **Results**: View biomechanical insights in profile detail page

## Error Handling

- **Video Download Failures**: Retry mechanism with user feedback
- **Analysis Failures**: Error logging and user notification
- **Database Errors**: Graceful degradation with local storage fallback

## Deployment Considerations

### Vercel Deployment (Frontend)

Set environment variables in Vercel dashboard:
```bash
BACKEND_API_URL="https://your-backend-domain.com"
DATABASE_URL="your-production-database-url"
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
# ... other variables
```

### Backend Deployment

Ensure the backend is deployed and accessible from the frontend domain. Update CORS settings in the backend to allow requests from your frontend domain.

## Monitoring and Logging

- Backend logs analysis progress and errors
- Database operations are logged for debugging
- Frontend provides user feedback during analysis

## Security

- All video URLs are signed and time-limited
- Database access uses service role keys server-side only
- User authentication required for all operations

## Troubleshooting

### Common Issues

1. **Backend not accessible**: Check `BACKEND_API_URL` configuration
2. **Database connection failed**: Verify `DATABASE_URL` and network access
3. **OpenPose errors**: Ensure OpenPose is properly installed and configured
4. **Video download timeout**: Check video URL accessibility and file size

### Debug Commands

```bash
# Check backend health
curl http://localhost:8000/health

# Test video analysis
curl -X POST http://localhost:8000/analyze-url \
  -H "Content-Type: application/json" \
  -d '{"video_url":"test-url","profile_id":"test","video_type":"sagittal","user_id":"test"}'
```

## Future Enhancements

- Real-time analysis progress updates via WebSockets
- Batch video processing for multiple angles
- Advanced biomechanical metrics
- Integration with wearable device data
- Machine learning model improvements
