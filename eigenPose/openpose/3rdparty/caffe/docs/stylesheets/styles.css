@import url(http://fonts.googleapis.com/css?family=PT+Serif|Open+Sans:600,400);

body {
  padding:10px 50px 0 0;
  font-family: 'Open Sans', sans-serif;
  font-size: 14px;
  color: #232323;
  background-color: #FBFAF7;
  margin: 0;
  line-height: 1.5rem;
  -webkit-font-smoothing: antialiased;
}

h1, h2, h3, h4, h5, h6 {
  color:#232323;
  margin:36px 0 10px;
}

p, ul, ol, table, dl {
  margin:0 0 22px;
}

h1, h2, h3 {
  font-family: 'PT Serif', serif;
  line-height:1.3;
  font-weight: normal;
  display: block;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

h1 {
  font-size: 30px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 18px;
}

h4, h5, h6 {
  font-family: 'PT Serif', serif;
  font-weight: 700;
}

a {
  color:#C30000;
  text-decoration:none;
}

a:hover {
  text-decoration: underline;
}

a small {
  font-size: 12px;
}

em {
  font-style: italic;
}

strong {
  font-weight:700;
}

ul {
  padding-left: 25px;
}

ol {
  list-style: decimal;
  padding-left: 20px;
}

blockquote {
  margin: 0;
  padding: 0 0 0 20px;
  font-style: italic;
}

dl, dt, dd, dl p {
  font-color: #444;
}

dl dt {
  font-weight: bold;
}

dl dd {
  padding-left: 20px;
  font-style: italic;
}

dl p {
  padding-left: 20px;
  font-style: italic;
}

hr {
  border:0;
  background:#ccc;
  height:1px;
  margin:0 0 24px;
}

/* Images */

img {
  position: relative;
  margin: 0 auto;
  max-width: 650px;
  padding: 5px;
  margin: 10px 0 32px 0;
  border: 1px solid #ccc;
}

p img {
  display: inline;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  text-align: center;
  border: none;
}

/* Code blocks */
code, pre {
  font-family: monospace;
  color:#000;
  font-size:12px;
  line-height: 14px;
}

pre {
  padding: 6px 12px;
  background: #FDFEFB;
  border-radius:4px;
  border:1px solid #D7D8C8;
  overflow: auto;
  white-space: pre-wrap;
  margin-bottom: 16px;
}


/* Tables */
table {
  width:100%;
}

table {
  border: 1px solid #ccc;
  margin-bottom: 32px;
  text-align: left;
 }

th {
  font-family: 'Open Sans', sans-serif;
  font-size: 18px;
  font-weight: normal;
  padding: 10px;
  background: #232323;
  color: #FDFEFB;
 }

td {
  padding: 10px;
  background: #ccc;
 }


/* Wrapper */
.wrapper {
  width:960px;
}


/* Header */

header {
  width:170px;
  float:left;
  position:fixed;
  padding: 12px 25px 22px 50px;
  margin: 24px 25px 0 0;
}

p.header {
  font-size: 14px;
}

h1.header {
  font-size: 30px;
  font-weight: 300;
  line-height: 1.3em;
  margin-top: 0;
}

a.name {
  white-space: nowrap;
}

header ul {
  list-style:none;
  padding:0;
}

header li {
  list-style-type: none;
  width:132px;
  height:15px;
  margin-bottom: 12px;
  line-height: 1em;
  padding: 6px 6px 6px 7px;
  background: #c30000;
  border-radius:4px;
  border:1px solid #555;
}

header li:hover {
  background: #dd0000;
}

a.buttons {
  color: #fff;
  text-decoration: none;
  font-weight: normal;
  padding: 2px 2px 2px 22px;
  height: 30px;
}

a.github {
  background: url(/images/GitHub-Mark-64px.png) no-repeat center left;
  background-size: 15%;
}

/* Section - for main page content */

section {
  width:650px;
  float:right;
  padding-bottom:50px;
}

p.footnote {
  font-size: 12px;
}


/* Footer */

footer {
  width:170px;
  float:left;
  position:fixed;
  bottom:10px;
  padding-left: 50px;
}

@media print, screen and (max-width: 960px) {

  div.wrapper {
    width:auto;
    margin:0;
  }

  header, section, footer {
    float:none;
    position:static;
    width:auto;
  }

  footer {
    border-top: 1px solid #ccc;
    margin:0 84px 0 50px;
    padding:0;
  }

  header {
    padding-right:320px;
  }

  section {
    padding:20px 84px 20px 50px;
    margin:0 0 20px;
  }

  header a small {
    display:inline;
  }

  header ul {
    position:absolute;
    right:130px;
    top:84px;
  }
}

@media print, screen and (max-width: 720px) {
  body {
    word-wrap:break-word;
  }

  header {
    padding:10px 20px 0;
    margin-right: 0;
  }

  section {
    padding:10px 0 10px 20px;
    margin:0 0 30px;
  }

  footer {
    margin: 0 0 0 30px;
  }

  header ul, header p.view {
    position:static;
  }
}

@media print, screen and (max-width: 480px) {

  header ul li.download {
    display:none;
  }

  footer {
    margin: 0 0 0 20px;
  }

  footer a{
    display:block;
  }

}

@media print {
  body {
    padding:0.4in;
    font-size:12pt;
    color:#444;
  }
}
