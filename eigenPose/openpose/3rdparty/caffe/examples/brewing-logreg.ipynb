{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Brewing Logistic Regression then Going Deeper\n", "\n", "While Caffe is made for deep networks it can likewise represent \"shallow\" models like logistic regression for classification. We'll do simple logistic regression on synthetic data that we'll generate and save to HDF5 to feed vectors to Caffe. Once that model is done, we'll add layers to improve accuracy. That's what Caffe is about: define a model, experiment, and then deploy."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "os.ch<PERSON>('..')\n", "\n", "import sys\n", "sys.path.insert(0, './python')\n", "import caffe\n", "\n", "\n", "import os\n", "import h5py\n", "import shutil\n", "import tempfile\n", "\n", "import sklearn\n", "import sklearn.datasets\n", "import sklearn.linear_model\n", "\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Synthesize a dataset of 10,000 4-vectors for binary classification with 2 informative features and 2 noise features."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiMAAAImCAYAAACB54oCAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nVFRUMsr/B18y5d10ojlGAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x7f8611575a50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["X, y = sklearn.datasets.make_classification(\n", "    n_samples=10000, n_features=4, n_redundant=0, n_informative=2, \n", "    n_clusters_per_class=2, hypercube=False, random_state=0\n", ")\n", "\n", "# Split into train and test\n", "X, Xt, y, yt = sklearn.model_selection.train_test_split(X, y)\n", "\n", "# Visualize sample of the data\n", "ind = np.random.permutation(X.shape[0])[:1000]\n", "df = pd.DataFrame(X[ind])\n", "_ = pd.plotting.scatter_matrix(df, figsize=(9, 9), diagonal='kde', marker='o', s=40, alpha=.4, c=y[ind])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Learn and evaluate scikit-learn's logistic regression with stochastic gradient descent (SGD) training. Time and check the classifier's accuracy."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.781\n", "Accuracy: 0.781\n", "Accuracy: 0.781\n", "Accuracy: 0.781\n", "1 loop, best of 3: 372 ms per loop\n"]}], "source": ["%%timeit\n", "# Train and test the scikit-learn SGD logistic regression.\n", "clf = sklearn.linear_model.SGDClassifier(\n", "    loss='log', n_iter=1000, penalty='l2', alpha=5e-4, class_weight='balanced')\n", "\n", "clf.fit(X, y)\n", "yt_pred = clf.predict(Xt)\n", "print('Accuracy: {:.3f}'.format(sklearn.metrics.accuracy_score(yt, yt_pred)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Save the dataset to HDF5 for loading in Caffe."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Write out the data to HDF5 files in a temp directory.\n", "# This file is assumed to be caffe_root/examples/hdf5_classification.ipynb\n", "dirname = os.path.abspath('./examples/hdf5_classification/data')\n", "if not os.path.exists(dirname):\n", "    os.makedirs(dirname)\n", "\n", "train_filename = os.path.join(dirname, 'train.h5')\n", "test_filename = os.path.join(dirname, 'test.h5')\n", "\n", "# HDF5DataLayer source should be a file containing a list of HDF5 filenames.\n", "# To show this off, we'll list the same data file twice.\n", "with h5py.File(train_filename, 'w') as f:\n", "    f['data'] = X\n", "    f['label'] = y.astype(np.float32)\n", "with open(os.path.join(dirname, 'train.txt'), 'w') as f:\n", "    f.write(train_filename + '\\n')\n", "    f.write(train_filename + '\\n')\n", "    \n", "# HDF5 is pretty efficient, but can be further compressed.\n", "comp_kwargs = {'compression': 'gzip', 'compression_opts': 1}\n", "with h5py.File(test_filename, 'w') as f:\n", "    f.create_dataset('data', data=Xt, **comp_kwargs)\n", "    f.create_dataset('label', data=yt.astype(np.float32), **comp_kwargs)\n", "with open(os.path.join(dirname, 'test.txt'), 'w') as f:\n", "    f.write(test_filename + '\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's define logistic regression in Caffe through Python net specification. This is a quick and natural way to define nets that sidesteps manually editing the protobuf model."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [], "source": ["from caffe import layers as L\n", "from caffe import params as P\n", "\n", "def logreg(hdf5, batch_size):\n", "    # logistic regression: data, matrix multiplication, and 2-class softmax loss\n", "    n = caffe.NetSpec()\n", "    n.data, n.label = L.HDF5Data(batch_size=batch_size, source=hdf5, ntop=2)\n", "    n.ip1 = L.InnerProduct(n.data, num_output=2, weight_filler=dict(type='xavier'))\n", "    n.accuracy = L.Accuracy(n.ip1, n.label)\n", "    n.loss = <PERSON><PERSON>(n.ip1, n.label)\n", "    return n.to_proto()\n", "\n", "train_net_path = 'examples/hdf5_classification/logreg_auto_train.prototxt'\n", "with open(train_net_path, 'w') as f:\n", "    f.write(str(logreg('examples/hdf5_classification/data/train.txt', 10)))\n", "\n", "test_net_path = 'examples/hdf5_classification/logreg_auto_test.prototxt'\n", "with open(test_net_path, 'w') as f:\n", "    f.write(str(logreg('examples/hdf5_classification/data/test.txt', 10)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we'll define our \"solver\" which trains the network by specifying the locations of the train and test nets we defined above, as well as setting values for various parameters used for learning, display, and \"snapshotting\"."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [], "source": ["from caffe.proto import caffe_pb2\n", "\n", "def solver(train_net_path, test_net_path):\n", "    s = caffe_pb2.SolverParameter()\n", "\n", "    # Specify locations of the train and test networks.\n", "    s.train_net = train_net_path\n", "    s.test_net.append(test_net_path)\n", "\n", "    s.test_interval = 1000  # Test after every 1000 training iterations.\n", "    s.test_iter.append(250) # Test 250 \"batches\" each time we test.\n", "\n", "    s.max_iter = 10000      # # of times to update the net (training iterations)\n", "\n", "    # Set the initial learning rate for stochastic gradient descent (SGD).\n", "    s.base_lr = 0.01        \n", "\n", "    # Set `lr_policy` to define how the learning rate changes during training.\n", "    # Here, we 'step' the learning rate by multiplying it by a factor `gamma`\n", "    # every `stepsize` iterations.\n", "    s.lr_policy = 'step'\n", "    s.gamma = 0.1\n", "    s.stepsize = 5000\n", "\n", "    # Set other optimization parameters. Setting a non-zero `momentum` takes a\n", "    # weighted average of the current gradient and previous gradients to make\n", "    # learning more stable. L2 weight decay regularizes learning, to help prevent\n", "    # the model from overfitting.\n", "    s.momentum = 0.9\n", "    s.weight_decay = 5e-4\n", "\n", "    # Display the current training loss and accuracy every 1000 iterations.\n", "    s.display = 1000\n", "\n", "    # Snapshots are files used to store networks we've trained.  Here, we'll\n", "    # snapshot every 10K iterations -- just once at the end of training.\n", "    # For larger networks that take longer to train, you may want to set\n", "    # snapshot < max_iter to save the network and training state to disk during\n", "    # optimization, preventing disaster in case of machine crashes, etc.\n", "    s.snapshot = 10000\n", "    s.snapshot_prefix = 'examples/hdf5_classification/data/train'\n", "\n", "    # We'll train on the CPU for fair benchmarking against scikit-learn.\n", "    # Changing to GPU should result in much faster training!\n", "    s.solver_mode = caffe_pb2.SolverParameter.CPU\n", "    \n", "    return s\n", "\n", "solver_path = 'examples/hdf5_classification/logreg_solver.prototxt'\n", "with open(solver_path, 'w') as f:\n", "    f.write(str(solver(train_net_path, test_net_path)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Time to learn and evaluate our Caffeinated logistic regression in Python."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.770\n", "Accuracy: 0.770\n", "Accuracy: 0.770\n", "Accuracy: 0.770\n", "1 loop, best of 3: 195 ms per loop\n"]}], "source": ["%%timeit\n", "caffe.set_mode_cpu()\n", "solver = caffe.get_solver(solver_path)\n", "solver.solve()\n", "\n", "accuracy = 0\n", "batch_size = solver.test_nets[0].blobs['data'].num\n", "test_iters = int(len(Xt) / batch_size)\n", "for i in range(test_iters):\n", "    solver.test_nets[0].forward()\n", "    accuracy += solver.test_nets[0].blobs['accuracy'].data\n", "accuracy /= test_iters\n", "\n", "print(\"Accuracy: {:.3f}\".format(accuracy))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Do the same through the command line interface for detailed output on the model and solving."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I0224 00:32:03.232779   655 caffe.cpp:178] Use CPU.\n", "I0224 00:32:03.391911   655 solver.cpp:48] Initializing solver from parameters: \n", "train_net: \"examples/hdf5_classification/logreg_auto_train.prototxt\"\n", "test_net: \"examples/hdf5_classification/logreg_auto_test.prototxt\"\n", "test_iter: 250\n", "test_interval: 1000\n", "base_lr: 0.01\n", "display: 1000\n", "max_iter: 10000\n", "lr_policy: \"step\"\n", "gamma: 0.1\n", "momentum: 0.9\n", "weight_decay: 0.0005\n", "stepsize: 5000\n", "snapshot: 10000\n", "snapshot_prefix: \"examples/hdf5_classification/data/train\"\n", "solver_mode: CPU\n", "I0224 00:32:03.392065   655 solver.cpp:81] Creating training net from train_net file: examples/hdf5_classification/logreg_auto_train.prototxt\n", "I0224 00:32:03.392215   655 net.cpp:49] Initializing net from parameters: \n", "state {\n", "  phase: TRAIN\n", "}\n", "layer {\n", "  name: \"data\"\n", "  type: \"HDF5Data\"\n", "  top: \"data\"\n", "  top: \"label\"\n", "  hdf5_data_param {\n", "    source: \"examples/hdf5_classification/data/train.txt\"\n", "    batch_size: 10\n", "  }\n", "}\n", "layer {\n", "  name: \"ip1\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"data\"\n", "  top: \"ip1\"\n", "  inner_product_param {\n", "    num_output: 2\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"accuracy\"\n", "  type: \"Accuracy\"\n", "  bottom: \"ip1\"\n", "  bottom: \"label\"\n", "  top: \"accuracy\"\n", "}\n", "layer {\n", "  name: \"loss\"\n", "  type: \"SoftmaxWithLoss\"\n", "  bottom: \"ip1\"\n", "  bottom: \"label\"\n", "  top: \"loss\"\n", "}\n", "I0224 00:32:03.392365   655 layer_factory.hpp:77] Creating layer data\n", "I0224 00:32:03.392382   655 net.cpp:106] Creating Layer data\n", "I0224 00:32:03.392395   655 net.cpp:411] data -> data\n", "I0224 00:32:03.392423   655 net.cpp:411] data -> label\n", "I0224 00:32:03.392442   655 hdf5_data_layer.cpp:79] Loading list of HDF5 filenames from: examples/hdf5_classification/data/train.txt\n", "I0224 00:32:03.392473   655 hdf5_data_layer.cpp:93] Number of HDF5 files: 2\n", "I0224 00:32:03.393473   655 hdf5.cpp:32] Datatype class: H5T_FLOAT\n", "I0224 00:32:03.393862   655 net.cpp:150] Setting up data\n", "I0224 00:32:03.393884   655 net.cpp:157] Top shape: 10 4 (40)\n", "I0224 00:32:03.393894   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.393901   655 net.cpp:165] Memory required for data: 200\n", "I0224 00:32:03.393911   655 layer_factory.hpp:77] Creating layer label_data_1_split\n", "I0224 00:32:03.393924   655 net.cpp:106] Creating Layer label_data_1_split\n", "I0224 00:32:03.393934   655 net.cpp:454] label_data_1_split <- label\n", "I0224 00:32:03.393945   655 net.cpp:411] label_data_1_split -> label_data_1_split_0\n", "I0224 00:32:03.393956   655 net.cpp:411] label_data_1_split -> label_data_1_split_1\n", "I0224 00:32:03.393970   655 net.cpp:150] Setting up label_data_1_split\n", "I0224 00:32:03.393978   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.393986   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.393995   655 net.cpp:165] Memory required for data: 280\n", "I0224 00:32:03.394001   655 layer_factory.hpp:77] Creating layer ip1\n", "I0224 00:32:03.394012   655 net.cpp:106] Creating Layer ip1\n", "I0224 00:32:03.394021   655 net.cpp:454] ip1 <- data\n", "I0224 00:32:03.394029   655 net.cpp:411] ip1 -> ip1\n", "I0224 00:32:03.394311   655 net.cpp:150] Setting up ip1\n", "I0224 00:32:03.394323   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.394331   655 net.cpp:165] Memory required for data: 360\n", "I0224 00:32:03.394348   655 layer_factory.hpp:77] Creating layer ip1_ip1_0_split\n", "I0224 00:32:03.394358   655 net.cpp:106] Creating Layer ip1_ip1_0_split\n", "I0224 00:32:03.394366   655 net.cpp:454] ip1_ip1_0_split <- ip1\n", "I0224 00:32:03.394374   655 net.cpp:411] ip1_ip1_0_split -> ip1_ip1_0_split_0\n", "I0224 00:32:03.394386   655 net.cpp:411] ip1_ip1_0_split -> ip1_ip1_0_split_1\n", "I0224 00:32:03.394395   655 net.cpp:150] Setting up ip1_ip1_0_split\n", "I0224 00:32:03.394404   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.394424   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.394443   655 net.cpp:165] Memory required for data: 520\n", "I0224 00:32:03.394450   655 layer_factory.hpp:77] Creating layer accuracy\n", "I0224 00:32:03.394462   655 net.cpp:106] Creating Layer accuracy\n", "I0224 00:32:03.394479   655 net.cpp:454] accuracy <- ip1_ip1_0_split_0\n", "I0224 00:32:03.394489   655 net.cpp:454] accuracy <- label_data_1_split_0\n", "I0224 00:32:03.394497   655 net.cpp:411] accuracy -> accuracy\n", "I0224 00:32:03.394510   655 net.cpp:150] Setting up accuracy\n", "I0224 00:32:03.394536   655 net.cpp:157] Top shape: (1)\n", "I0224 00:32:03.394543   655 net.cpp:165] Memory required for data: 524\n", "I0224 00:32:03.394551   655 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:03.394562   655 net.cpp:106] Creating Layer loss\n", "I0224 00:32:03.394569   655 net.cpp:454] loss <- ip1_ip1_0_split_1\n", "I0224 00:32:03.394577   655 net.cpp:454] loss <- label_data_1_split_1\n", "I0224 00:32:03.394587   655 net.cpp:411] loss -> loss\n", "I0224 00:32:03.394603   655 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:03.394624   655 net.cpp:150] Setting up loss\n", "I0224 00:32:03.394634   655 net.cpp:157] Top shape: (1)\n", "I0224 00:32:03.394641   655 net.cpp:160]     with loss weight 1\n", "I0224 00:32:03.394659   655 net.cpp:165] Memory required for data: 528\n", "I0224 00:32:03.394665   655 net.cpp:226] loss needs backward computation.\n", "I0224 00:32:03.394673   655 net.cpp:228] accuracy does not need backward computation.\n", "I0224 00:32:03.394682   655 net.cpp:226] ip1_ip1_0_split needs backward computation.\n", "I0224 00:32:03.394690   655 net.cpp:226] ip1 needs backward computation.\n", "I0224 00:32:03.394697   655 net.cpp:228] label_data_1_split does not need backward computation.\n", "I0224 00:32:03.394706   655 net.cpp:228] data does not need backward computation.\n", "I0224 00:32:03.394712   655 net.cpp:270] This network produces output accuracy\n", "I0224 00:32:03.394721   655 net.cpp:270] This network produces output loss\n", "I0224 00:32:03.394731   655 net.cpp:283] Network initialization done.\n", "I0224 00:32:03.394804   655 solver.cpp:181] Creating test net (#0) specified by test_net file: examples/hdf5_classification/logreg_auto_test.prototxt\n", "I0224 00:32:03.394836   655 net.cpp:49] Initializing net from parameters: \n", "state {\n", "  phase: TEST\n", "}\n", "layer {\n", "  name: \"data\"\n", "  type: \"HDF5Data\"\n", "  top: \"data\"\n", "  top: \"label\"\n", "  hdf5_data_param {\n", "    source: \"examples/hdf5_classification/data/test.txt\"\n", "    batch_size: 10\n", "  }\n", "}\n", "layer {\n", "  name: \"ip1\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"data\"\n", "  top: \"ip1\"\n", "  inner_product_param {\n", "    num_output: 2\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"accuracy\"\n", "  type: \"Accuracy\"\n", "  bottom: \"ip1\"\n", "  bottom: \"label\"\n", "  top: \"accuracy\"\n", "}\n", "layer {\n", "  name: \"loss\"\n", "  type: \"SoftmaxWithLoss\"\n", "  bottom: \"ip1\"\n", "  bottom: \"label\"\n", "  top: \"loss\"\n", "}\n", "I0224 00:32:03.394953   655 layer_factory.hpp:77] Creating layer data\n", "I0224 00:32:03.394964   655 net.cpp:106] Creating Layer data\n", "I0224 00:32:03.394973   655 net.cpp:411] data -> data\n", "I0224 00:32:03.394984   655 net.cpp:411] data -> label\n", "I0224 00:32:03.394994   655 hdf5_data_layer.cpp:79] Loading list of HDF5 filenames from: examples/hdf5_classification/data/test.txt\n", "I0224 00:32:03.395009   655 hdf5_data_layer.cpp:93] Number of HDF5 files: 1\n", "I0224 00:32:03.395937   655 net.cpp:150] Setting up data\n", "I0224 00:32:03.395953   655 net.cpp:157] Top shape: 10 4 (40)\n", "I0224 00:32:03.395963   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.395970   655 net.cpp:165] Memory required for data: 200\n", "I0224 00:32:03.395978   655 layer_factory.hpp:77] Creating layer label_data_1_split\n", "I0224 00:32:03.395989   655 net.cpp:106] Creating Layer label_data_1_split\n", "I0224 00:32:03.395997   655 net.cpp:454] label_data_1_split <- label\n", "I0224 00:32:03.396005   655 net.cpp:411] label_data_1_split -> label_data_1_split_0\n", "I0224 00:32:03.396016   655 net.cpp:411] label_data_1_split -> label_data_1_split_1\n", "I0224 00:32:03.396028   655 net.cpp:150] Setting up label_data_1_split\n", "I0224 00:32:03.396036   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.396044   655 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:03.396051   655 net.cpp:165] Memory required for data: 280\n", "I0224 00:32:03.396059   655 layer_factory.hpp:77] Creating layer ip1\n", "I0224 00:32:03.396069   655 net.cpp:106] Creating Layer ip1\n", "I0224 00:32:03.396075   655 net.cpp:454] ip1 <- data\n", "I0224 00:32:03.396085   655 net.cpp:411] ip1 -> ip1\n", "I0224 00:32:03.396100   655 net.cpp:150] Setting up ip1\n", "I0224 00:32:03.396109   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.396116   655 net.cpp:165] Memory required for data: 360\n", "I0224 00:32:03.396138   655 layer_factory.hpp:77] Creating layer ip1_ip1_0_split\n", "I0224 00:32:03.396148   655 net.cpp:106] Creating Layer ip1_ip1_0_split\n", "I0224 00:32:03.396157   655 net.cpp:454] ip1_ip1_0_split <- ip1\n", "I0224 00:32:03.396164   655 net.cpp:411] ip1_ip1_0_split -> ip1_ip1_0_split_0\n", "I0224 00:32:03.396174   655 net.cpp:411] ip1_ip1_0_split -> ip1_ip1_0_split_1\n", "I0224 00:32:03.396185   655 net.cpp:150] Setting up ip1_ip1_0_split\n", "I0224 00:32:03.396194   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.396203   655 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:03.396209   655 net.cpp:165] Memory required for data: 520\n", "I0224 00:32:03.396216   655 layer_factory.hpp:77] Creating layer accuracy\n", "I0224 00:32:03.396225   655 net.cpp:106] Creating Layer accuracy\n", "I0224 00:32:03.396234   655 net.cpp:454] accuracy <- ip1_ip1_0_split_0\n", "I0224 00:32:03.396241   655 net.cpp:454] accuracy <- label_data_1_split_0\n", "I0224 00:32:03.396250   655 net.cpp:411] accuracy -> accuracy\n", "I0224 00:32:03.396260   655 net.cpp:150] Setting up accuracy\n", "I0224 00:32:03.396270   655 net.cpp:157] Top shape: (1)\n", "I0224 00:32:03.396276   655 net.cpp:165] Memory required for data: 524\n", "I0224 00:32:03.396283   655 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:03.396291   655 net.cpp:106] Creating Layer loss\n", "I0224 00:32:03.396299   655 net.cpp:454] loss <- ip1_ip1_0_split_1\n", "I0224 00:32:03.396307   655 net.cpp:454] loss <- label_data_1_split_1\n", "I0224 00:32:03.396317   655 net.cpp:411] loss -> loss\n", "I0224 00:32:03.396327   655 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:03.396339   655 net.cpp:150] Setting up loss\n", "I0224 00:32:03.396349   655 net.cpp:157] Top shape: (1)\n", "I0224 00:32:03.396356   655 net.cpp:160]     with loss weight 1\n", "I0224 00:32:03.396365   655 net.cpp:165] Memory required for data: 528\n", "I0224 00:32:03.396373   655 net.cpp:226] loss needs backward computation.\n", "I0224 00:32:03.396381   655 net.cpp:228] accuracy does not need backward computation.\n", "I0224 00:32:03.396389   655 net.cpp:226] ip1_ip1_0_split needs backward computation.\n", "I0224 00:32:03.396396   655 net.cpp:226] ip1 needs backward computation.\n", "I0224 00:32:03.396404   655 net.cpp:228] label_data_1_split does not need backward computation.\n", "I0224 00:32:03.396412   655 net.cpp:228] data does not need backward computation.\n", "I0224 00:32:03.396420   655 net.cpp:270] This network produces output accuracy\n", "I0224 00:32:03.396427   655 net.cpp:270] This network produces output loss\n", "I0224 00:32:03.396437   655 net.cpp:283] Network initialization done.\n", "I0224 00:32:03.396455   655 solver.cpp:60] Solver scaffolding done.\n", "I0224 00:32:03.396473   655 caffe.cpp:219] Starting Optimization\n", "I0224 00:32:03.396482   655 solver.cpp:280] Solving \n", "I0224 00:32:03.396489   655 solver.cpp:281] Learning Rate Policy: step\n", "I0224 00:32:03.396499   655 solver.cpp:338] Iteration 0, Testing net (#0)\n", "I0224 00:32:03.932615   655 solver.cpp:406]     Test net output #0: accuracy = 0.4268\n", "I0224 00:32:03.932656   655 solver.cpp:406]     Test net output #1: loss = 1.33093 (* 1 = 1.33093 loss)\n", "I0224 00:32:03.932723   655 solver.cpp:229] Iteration 0, loss = 1.06081\n", "I0224 00:32:03.932737   655 solver.cpp:245]     Train net output #0: accuracy = 0.4\n", "I0224 00:32:03.932749   655 solver.cpp:245]     Train net output #1: loss = 1.06081 (* 1 = 1.06081 loss)\n", "I0224 00:32:03.932765   655 sgd_solver.cpp:106] Iteration 0, lr = 0.01\n", "I0224 00:32:03.945551   655 solver.cpp:338] Iteration 1000, Testing net (#0)\n", "I0224 00:32:03.948048   655 solver.cpp:406]     Test net output #0: accuracy = 0.694\n", "I0224 00:32:03.948065   655 solver.cpp:406]     Test net output #1: loss = 0.60406 (* 1 = 0.60406 loss)\n", "I0224 00:32:03.948091   655 solver.cpp:229] Iteration 1000, loss = 0.505853\n", "I0224 00:32:03.948102   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:03.948113   655 solver.cpp:245]     Train net output #1: loss = 0.505853 (* 1 = 0.505853 loss)\n", "I0224 00:32:03.948122   655 sgd_solver.cpp:106] Iteration 1000, lr = 0.01\n", "I0224 00:32:03.960741   655 solver.cpp:338] Iteration 2000, Testing net (#0)\n", "I0224 00:32:03.963214   655 solver.cpp:406]     Test net output #0: accuracy = 0.7372\n", "I0224 00:32:03.963249   655 solver.cpp:406]     Test net output #1: loss = 0.595267 (* 1 = 0.595267 loss)\n", "I0224 00:32:03.963276   655 solver.cpp:229] Iteration 2000, loss = 0.549211\n", "I0224 00:32:03.963289   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:03.963299   655 solver.cpp:245]     Train net output #1: loss = 0.549211 (* 1 = 0.549211 loss)\n", "I0224 00:32:03.963309   655 sgd_solver.cpp:106] Iteration 2000, lr = 0.01\n", "I0224 00:32:03.975945   655 solver.cpp:338] Iteration 3000, Testing net (#0)\n", "I0224 00:32:03.978435   655 solver.cpp:406]     Test net output #0: accuracy = 0.7732\n", "I0224 00:32:03.978451   655 solver.cpp:406]     Test net output #1: loss = 0.594998 (* 1 = 0.594998 loss)\n", "I0224 00:32:03.978884   655 solver.cpp:229] Iteration 3000, loss = 0.66133\n", "I0224 00:32:03.978911   655 solver.cpp:245]     Train net output #0: accuracy = 0.8\n", "I0224 00:32:03.978932   655 solver.cpp:245]     Train net output #1: loss = 0.66133 (* 1 = 0.66133 loss)\n", "I0224 00:32:03.978950   655 sgd_solver.cpp:106] Iteration 3000, lr = 0.01\n", "I0224 00:32:03.992017   655 solver.cpp:338] Iteration 4000, Testing net (#0)\n", "I0224 00:32:03.994509   655 solver.cpp:406]     Test net output #0: accuracy = 0.694\n", "I0224 00:32:03.994525   655 solver.cpp:406]     Test net output #1: loss = 0.60406 (* 1 = 0.60406 loss)\n", "I0224 00:32:03.994551   655 solver.cpp:229] Iteration 4000, loss = 0.505853\n", "I0224 00:32:03.994562   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:03.994573   655 solver.cpp:245]     Train net output #1: loss = 0.505853 (* 1 = 0.505853 loss)\n", "I0224 00:32:03.994583   655 sgd_solver.cpp:106] Iteration 4000, lr = 0.01\n", "I0224 00:32:04.007200   655 solver.cpp:338] Iteration 5000, Testing net (#0)\n", "I0224 00:32:04.009686   655 solver.cpp:406]     Test net output #0: accuracy = 0.7372\n", "I0224 00:32:04.009702   655 solver.cpp:406]     Test net output #1: loss = 0.595267 (* 1 = 0.595267 loss)\n", "I0224 00:32:04.009727   655 solver.cpp:229] Iteration 5000, loss = 0.549211\n", "I0224 00:32:04.009738   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:04.009749   655 solver.cpp:245]     Train net output #1: loss = 0.549211 (* 1 = 0.549211 loss)\n", "I0224 00:32:04.009758   655 sgd_solver.cpp:106] Iteration 5000, lr = 0.001\n", "I0224 00:32:04.022734   655 solver.cpp:338] Iteration 6000, Testing net (#0)\n", "I0224 00:32:04.025177   655 solver.cpp:406]     Test net output #0: accuracy = 0.7824\n", "I0224 00:32:04.025193   655 solver.cpp:406]     Test net output #1: loss = 0.593367 (* 1 = 0.593367 loss)\n", "I0224 00:32:04.025545   655 solver.cpp:229] Iteration 6000, loss = 0.654873\n", "I0224 00:32:04.025562   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:04.025573   655 solver.cpp:245]     Train net output #1: loss = 0.654873 (* 1 = 0.654873 loss)\n", "I0224 00:32:04.025583   655 sgd_solver.cpp:106] Iteration 6000, lr = 0.001\n", "I0224 00:32:04.038586   655 solver.cpp:338] Iteration 7000, Testing net (#0)\n", "I0224 00:32:04.041016   655 solver.cpp:406]     Test net output #0: accuracy = 0.7704\n", "I0224 00:32:04.041033   655 solver.cpp:406]     Test net output #1: loss = 0.593842 (* 1 = 0.593842 loss)\n", "I0224 00:32:04.041059   655 solver.cpp:229] Iteration 7000, loss = 0.46611\n", "I0224 00:32:04.041071   655 solver.cpp:245]     Train net output #0: accuracy = 0.6\n", "I0224 00:32:04.041082   655 solver.cpp:245]     Train net output #1: loss = 0.46611 (* 1 = 0.46611 loss)\n", "I0224 00:32:04.041091   655 sgd_solver.cpp:106] Iteration 7000, lr = 0.001\n", "I0224 00:32:04.053722   655 solver.cpp:338] Iteration 8000, Testing net (#0)\n", "I0224 00:32:04.056171   655 solver.cpp:406]     Test net output #0: accuracy = 0.7788\n", "I0224 00:32:04.056187   655 solver.cpp:406]     Test net output #1: loss = 0.592847 (* 1 = 0.592847 loss)\n", "I0224 00:32:04.056213   655 solver.cpp:229] Iteration 8000, loss = 0.615126\n", "I0224 00:32:04.056224   655 solver.cpp:245]     Train net output #0: accuracy = 0.8\n", "I0224 00:32:04.056236   655 solver.cpp:245]     Train net output #1: loss = 0.615126 (* 1 = 0.615126 loss)\n", "I0224 00:32:04.056244   655 sgd_solver.cpp:106] Iteration 8000, lr = 0.001\n", "I0224 00:32:04.068853   655 solver.cpp:338] Iteration 9000, Testing net (#0)\n", "I0224 00:32:04.071291   655 solver.cpp:406]     Test net output #0: accuracy = 0.7808\n", "I0224 00:32:04.071307   655 solver.cpp:406]     Test net output #1: loss = 0.593293 (* 1 = 0.593293 loss)\n", "I0224 00:32:04.071650   655 solver.cpp:229] Iteration 9000, loss = 0.654997\n", "I0224 00:32:04.071666   655 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:04.071677   655 solver.cpp:245]     Train net output #1: loss = 0.654998 (* 1 = 0.654998 loss)\n", "I0224 00:32:04.071687   655 sgd_solver.cpp:106] Iteration 9000, lr = 0.001\n", "I0224 00:32:04.084717   655 solver.cpp:456] Snapshotting to binary proto file examples/hdf5_classification/data/train_iter_10000.caffemodel\n", "I0224 00:32:04.084885   655 sgd_solver.cpp:273] Snapshotting solver state to binary proto file examples/hdf5_classification/data/train_iter_10000.solverstate\n", "I0224 00:32:04.084960   655 solver.cpp:318] Iteration 10000, loss = 0.466505\n", "I0224 00:32:04.084977   655 solver.cpp:338] Iteration 10000, Testing net (#0)\n", "I0224 00:32:04.087514   655 solver.cpp:406]     Test net output #0: accuracy = 0.77\n", "I0224 00:32:04.087532   655 solver.cpp:406]     Test net output #1: loss = 0.593815 (* 1 = 0.593815 loss)\n", "I0224 00:32:04.087541   655 solver.cpp:323] Optimization Done.\n", "I0224 00:32:04.087548   655 caffe.cpp:222] Optimization Done.\n"]}], "source": ["!./build/tools/caffe train -solver examples/hdf5_classification/logreg_solver.prototxt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you look at output or the `logreg_auto_train.prototxt`, you'll see that the model is simple logistic regression.\n", "We can make it a little more advanced by introducing a non-linearity between weights that take the input and weights that give the output -- now we have a two-layer network.\n", "That network is given in `nonlinear_auto_train.prototxt`, and that's the only change made in `nonlinear_logreg_solver.prototxt` which we will now use.\n", "\n", "The final accuracy of the new network should be higher than logistic regression!"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [], "source": ["from caffe import layers as L\n", "from caffe import params as P\n", "\n", "def nonlinear_net(hdf5, batch_size):\n", "    # one small nonlinearity, one leap for model kind\n", "    n = caffe.NetSpec()\n", "    n.data, n.label = L.HDF5Data(batch_size=batch_size, source=hdf5, ntop=2)\n", "    # define a hidden layer of dimension 40\n", "    n.ip1 = L.InnerProduct(n.data, num_output=40, weight_filler=dict(type='xavier'))\n", "    # transform the output through the ReLU (rectified linear) non-linearity\n", "    n.relu1 = L.ReLU(n.ip1, in_place=True)\n", "    # score the (now non-linear) features\n", "    n.ip2 = L.InnerProduct(n.ip1, num_output=2, weight_filler=dict(type='xavier'))\n", "    # same accuracy and loss as before\n", "    n.accuracy = L.Accuracy(n.ip2, n.label)\n", "    n.loss = <PERSON><PERSON>(n.ip2, n.label)\n", "    return n.to_proto()\n", "\n", "train_net_path = 'examples/hdf5_classification/nonlinear_auto_train.prototxt'\n", "with open(train_net_path, 'w') as f:\n", "    f.write(str(nonlinear_net('examples/hdf5_classification/data/train.txt', 10)))\n", "\n", "test_net_path = 'examples/hdf5_classification/nonlinear_auto_test.prototxt'\n", "with open(test_net_path, 'w') as f:\n", "    f.write(str(nonlinear_net('examples/hdf5_classification/data/test.txt', 10)))\n", "\n", "solver_path = 'examples/hdf5_classification/nonlinear_logreg_solver.prototxt'\n", "with open(solver_path, 'w') as f:\n", "    f.write(str(solver(train_net_path, test_net_path)))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.838\n", "Accuracy: 0.837\n", "Accuracy: 0.838\n", "Accuracy: 0.834\n", "1 loop, best of 3: 277 ms per loop\n"]}], "source": ["%%timeit\n", "caffe.set_mode_cpu()\n", "solver = caffe.get_solver(solver_path)\n", "solver.solve()\n", "\n", "accuracy = 0\n", "batch_size = solver.test_nets[0].blobs['data'].num\n", "test_iters = int(len(Xt) / batch_size)\n", "for i in range(test_iters):\n", "    solver.test_nets[0].forward()\n", "    accuracy += solver.test_nets[0].blobs['accuracy'].data\n", "accuracy /= test_iters\n", "\n", "print(\"Accuracy: {:.3f}\".format(accuracy))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Do the same through the command line interface for detailed output on the model and solving."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I0224 00:32:05.654265   658 caffe.cpp:178] Use CPU.\n", "I0224 00:32:05.810444   658 solver.cpp:48] Initializing solver from parameters: \n", "train_net: \"examples/hdf5_classification/nonlinear_auto_train.prototxt\"\n", "test_net: \"examples/hdf5_classification/nonlinear_auto_test.prototxt\"\n", "test_iter: 250\n", "test_interval: 1000\n", "base_lr: 0.01\n", "display: 1000\n", "max_iter: 10000\n", "lr_policy: \"step\"\n", "gamma: 0.1\n", "momentum: 0.9\n", "weight_decay: 0.0005\n", "stepsize: 5000\n", "snapshot: 10000\n", "snapshot_prefix: \"examples/hdf5_classification/data/train\"\n", "solver_mode: CPU\n", "I0224 00:32:05.810634   658 solver.cpp:81] Creating training net from train_net file: examples/hdf5_classification/nonlinear_auto_train.prototxt\n", "I0224 00:32:05.810835   658 net.cpp:49] Initializing net from parameters: \n", "state {\n", "  phase: TRAIN\n", "}\n", "layer {\n", "  name: \"data\"\n", "  type: \"HDF5Data\"\n", "  top: \"data\"\n", "  top: \"label\"\n", "  hdf5_data_param {\n", "    source: \"examples/hdf5_classification/data/train.txt\"\n", "    batch_size: 10\n", "  }\n", "}\n", "layer {\n", "  name: \"ip1\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"data\"\n", "  top: \"ip1\"\n", "  inner_product_param {\n", "    num_output: 40\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"relu1\"\n", "  type: \"ReLU\"\n", "  bottom: \"ip1\"\n", "  top: \"ip1\"\n", "}\n", "layer {\n", "  name: \"ip2\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"ip1\"\n", "  top: \"ip2\"\n", "  inner_product_param {\n", "    num_output: 2\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"accuracy\"\n", "  type: \"Accuracy\"\n", "  bottom: \"ip2\"\n", "  bottom: \"label\"\n", "  top: \"accuracy\"\n", "}\n", "layer {\n", "  name: \"loss\"\n", "  type: \"SoftmaxWithLoss\"\n", "  bottom: \"ip2\"\n", "  bottom: \"label\"\n", "  top: \"loss\"\n", "}\n", "I0224 00:32:05.811061   658 layer_factory.hpp:77] Creating layer data\n", "I0224 00:32:05.811079   658 net.cpp:106] Creating Layer data\n", "I0224 00:32:05.811092   658 net.cpp:411] data -> data\n", "I0224 00:32:05.811121   658 net.cpp:411] data -> label\n", "I0224 00:32:05.811143   658 hdf5_data_layer.cpp:79] Loading list of HDF5 filenames from: examples/hdf5_classification/data/train.txt\n", "I0224 00:32:05.811189   658 hdf5_data_layer.cpp:93] Number of HDF5 files: 2\n", "I0224 00:32:05.812254   658 hdf5.cpp:32] Datatype class: H5T_FLOAT\n", "I0224 00:32:05.812677   658 net.cpp:150] Setting up data\n", "I0224 00:32:05.812705   658 net.cpp:157] Top shape: 10 4 (40)\n", "I0224 00:32:05.812721   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.812729   658 net.cpp:165] Memory required for data: 200\n", "I0224 00:32:05.812739   658 layer_factory.hpp:77] Creating layer label_data_1_split\n", "I0224 00:32:05.812752   658 net.cpp:106] Creating Layer label_data_1_split\n", "I0224 00:32:05.812762   658 net.cpp:454] label_data_1_split <- label\n", "I0224 00:32:05.812774   658 net.cpp:411] label_data_1_split -> label_data_1_split_0\n", "I0224 00:32:05.812785   658 net.cpp:411] label_data_1_split -> label_data_1_split_1\n", "I0224 00:32:05.812798   658 net.cpp:150] Setting up label_data_1_split\n", "I0224 00:32:05.812808   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.812816   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.812824   658 net.cpp:165] Memory required for data: 280\n", "I0224 00:32:05.812831   658 layer_factory.hpp:77] Creating layer ip1\n", "I0224 00:32:05.812841   658 net.cpp:106] Creating Layer ip1\n", "I0224 00:32:05.812849   658 net.cpp:454] ip1 <- data\n", "I0224 00:32:05.812860   658 net.cpp:411] ip1 -> ip1\n", "I0224 00:32:05.813179   658 net.cpp:150] Setting up ip1\n", "I0224 00:32:05.813196   658 net.cpp:157] Top shape: 10 40 (400)\n", "I0224 00:32:05.813210   658 net.cpp:165] Memory required for data: 1880\n", "I0224 00:32:05.813230   658 layer_factory.hpp:77] Creating layer relu1\n", "I0224 00:32:05.813241   658 net.cpp:106] Creating Layer relu1\n", "I0224 00:32:05.813251   658 net.cpp:454] relu1 <- ip1\n", "I0224 00:32:05.813258   658 net.cpp:397] relu1 -> ip1 (in-place)\n", "I0224 00:32:05.813271   658 net.cpp:150] Setting up relu1\n", "I0224 00:32:05.813279   658 net.cpp:157] Top shape: 10 40 (400)\n", "I0224 00:32:05.813287   658 net.cpp:165] Memory required for data: 3480\n", "I0224 00:32:05.813294   658 layer_factory.hpp:77] Creating layer ip2\n", "I0224 00:32:05.813304   658 net.cpp:106] Creating Layer ip2\n", "I0224 00:32:05.813313   658 net.cpp:454] ip2 <- ip1\n", "I0224 00:32:05.813321   658 net.cpp:411] ip2 -> ip2\n", "I0224 00:32:05.813336   658 net.cpp:150] Setting up ip2\n", "I0224 00:32:05.813345   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.813379   658 net.cpp:165] Memory required for data: 3560\n", "I0224 00:32:05.813401   658 layer_factory.hpp:77] Creating layer ip2_ip2_0_split\n", "I0224 00:32:05.813417   658 net.cpp:106] Creating Layer ip2_ip2_0_split\n", "I0224 00:32:05.813426   658 net.cpp:454] ip2_ip2_0_split <- ip2\n", "I0224 00:32:05.813434   658 net.cpp:411] ip2_ip2_0_split -> ip2_ip2_0_split_0\n", "I0224 00:32:05.813446   658 net.cpp:411] ip2_ip2_0_split -> ip2_ip2_0_split_1\n", "I0224 00:32:05.813457   658 net.cpp:150] Setting up ip2_ip2_0_split\n", "I0224 00:32:05.813465   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.813473   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.813480   658 net.cpp:165] Memory required for data: 3720\n", "I0224 00:32:05.813488   658 layer_factory.hpp:77] Creating layer accuracy\n", "I0224 00:32:05.813499   658 net.cpp:106] Creating Layer accuracy\n", "I0224 00:32:05.813508   658 net.cpp:454] accuracy <- ip2_ip2_0_split_0\n", "I0224 00:32:05.813515   658 net.cpp:454] accuracy <- label_data_1_split_0\n", "I0224 00:32:05.813524   658 net.cpp:411] accuracy -> accuracy\n", "I0224 00:32:05.813539   658 net.cpp:150] Setting up accuracy\n", "I0224 00:32:05.813547   658 net.cpp:157] Top shape: (1)\n", "I0224 00:32:05.813555   658 net.cpp:165] Memory required for data: 3724\n", "I0224 00:32:05.813565   658 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:05.813585   658 net.cpp:106] Creating Layer loss\n", "I0224 00:32:05.813599   658 net.cpp:454] loss <- ip2_ip2_0_split_1\n", "I0224 00:32:05.813616   658 net.cpp:454] loss <- label_data_1_split_1\n", "I0224 00:32:05.813627   658 net.cpp:411] loss -> loss\n", "I0224 00:32:05.813642   658 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:05.813663   658 net.cpp:150] Setting up loss\n", "I0224 00:32:05.813671   658 net.cpp:157] Top shape: (1)\n", "I0224 00:32:05.813679   658 net.cpp:160]     with loss weight 1\n", "I0224 00:32:05.813695   658 net.cpp:165] Memory required for data: 3728\n", "I0224 00:32:05.813704   658 net.cpp:226] loss needs backward computation.\n", "I0224 00:32:05.813712   658 net.cpp:228] accuracy does not need backward computation.\n", "I0224 00:32:05.813720   658 net.cpp:226] ip2_ip2_0_split needs backward computation.\n", "I0224 00:32:05.813729   658 net.cpp:226] ip2 needs backward computation.\n", "I0224 00:32:05.813735   658 net.cpp:226] relu1 needs backward computation.\n", "I0224 00:32:05.813743   658 net.cpp:226] ip1 needs backward computation.\n", "I0224 00:32:05.813751   658 net.cpp:228] label_data_1_split does not need backward computation.\n", "I0224 00:32:05.813760   658 net.cpp:228] data does not need backward computation.\n", "I0224 00:32:05.813772   658 net.cpp:270] This network produces output accuracy\n", "I0224 00:32:05.813787   658 net.cpp:270] This network produces output loss\n", "I0224 00:32:05.813809   658 net.cpp:283] Network initialization done.\n", "I0224 00:32:05.813905   658 solver.cpp:181] Creating test net (#0) specified by test_net file: examples/hdf5_classification/nonlinear_auto_test.prototxt\n", "I0224 00:32:05.813944   658 net.cpp:49] Initializing net from parameters: \n", "state {\n", "  phase: TEST\n", "}\n", "layer {\n", "  name: \"data\"\n", "  type: \"HDF5Data\"\n", "  top: \"data\"\n", "  top: \"label\"\n", "  hdf5_data_param {\n", "    source: \"examples/hdf5_classification/data/test.txt\"\n", "    batch_size: 10\n", "  }\n", "}\n", "layer {\n", "  name: \"ip1\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"data\"\n", "  top: \"ip1\"\n", "  inner_product_param {\n", "    num_output: 40\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"relu1\"\n", "  type: \"ReLU\"\n", "  bottom: \"ip1\"\n", "  top: \"ip1\"\n", "}\n", "layer {\n", "  name: \"ip2\"\n", "  type: \"InnerProduct\"\n", "  bottom: \"ip1\"\n", "  top: \"ip2\"\n", "  inner_product_param {\n", "    num_output: 2\n", "    weight_filler {\n", "      type: \"xavier\"\n", "    }\n", "  }\n", "}\n", "layer {\n", "  name: \"accuracy\"\n", "  type: \"Accuracy\"\n", "  bottom: \"ip2\"\n", "  bottom: \"label\"\n", "  top: \"accuracy\"\n", "}\n", "layer {\n", "  name: \"loss\"\n", "  type: \"SoftmaxWithLoss\"\n", "  bottom: \"ip2\"\n", "  bottom: \"label\"\n", "  top: \"loss\"\n", "}\n", "I0224 00:32:05.814131   658 layer_factory.hpp:77] Creating layer data\n", "I0224 00:32:05.814142   658 net.cpp:106] Creating Layer data\n", "I0224 00:32:05.814152   658 net.cpp:411] data -> data\n", "I0224 00:32:05.814162   658 net.cpp:411] data -> label\n", "I0224 00:32:05.814180   658 hdf5_data_layer.cpp:79] Loading list of HDF5 filenames from: examples/hdf5_classification/data/test.txt\n", "I0224 00:32:05.814220   658 hdf5_data_layer.cpp:93] Number of HDF5 files: 1\n", "I0224 00:32:05.815207   658 net.cpp:150] Setting up data\n", "I0224 00:32:05.815227   658 net.cpp:157] Top shape: 10 4 (40)\n", "I0224 00:32:05.815243   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.815253   658 net.cpp:165] Memory required for data: 200\n", "I0224 00:32:05.815260   658 layer_factory.hpp:77] Creating layer label_data_1_split\n", "I0224 00:32:05.815270   658 net.cpp:106] Creating Layer label_data_1_split\n", "I0224 00:32:05.815279   658 net.cpp:454] label_data_1_split <- label\n", "I0224 00:32:05.815287   658 net.cpp:411] label_data_1_split -> label_data_1_split_0\n", "I0224 00:32:05.815299   658 net.cpp:411] label_data_1_split -> label_data_1_split_1\n", "I0224 00:32:05.815310   658 net.cpp:150] Setting up label_data_1_split\n", "I0224 00:32:05.815318   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.815326   658 net.cpp:157] Top shape: 10 (10)\n", "I0224 00:32:05.815335   658 net.cpp:165] Memory required for data: 280\n", "I0224 00:32:05.815341   658 layer_factory.hpp:77] Creating layer ip1\n", "I0224 00:32:05.815351   658 net.cpp:106] Creating Layer ip1\n", "I0224 00:32:05.815358   658 net.cpp:454] ip1 <- data\n", "I0224 00:32:05.815367   658 net.cpp:411] ip1 -> ip1\n", "I0224 00:32:05.815383   658 net.cpp:150] Setting up ip1\n", "I0224 00:32:05.815398   658 net.cpp:157] Top shape: 10 40 (400)\n", "I0224 00:32:05.815413   658 net.cpp:165] Memory required for data: 1880\n", "I0224 00:32:05.815435   658 layer_factory.hpp:77] Creating layer relu1\n", "I0224 00:32:05.815450   658 net.cpp:106] Creating Layer relu1\n", "I0224 00:32:05.815459   658 net.cpp:454] relu1 <- ip1\n", "I0224 00:32:05.815469   658 net.cpp:397] relu1 -> ip1 (in-place)\n", "I0224 00:32:05.815479   658 net.cpp:150] Setting up relu1\n", "I0224 00:32:05.815486   658 net.cpp:157] Top shape: 10 40 (400)\n", "I0224 00:32:05.815495   658 net.cpp:165] Memory required for data: 3480\n", "I0224 00:32:05.815501   658 layer_factory.hpp:77] Creating layer ip2\n", "I0224 00:32:05.815510   658 net.cpp:106] Creating Layer ip2\n", "I0224 00:32:05.815518   658 net.cpp:454] ip2 <- ip1\n", "I0224 00:32:05.815527   658 net.cpp:411] ip2 -> ip2\n", "I0224 00:32:05.815542   658 net.cpp:150] Setting up ip2\n", "I0224 00:32:05.815551   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.815559   658 net.cpp:165] Memory required for data: 3560\n", "I0224 00:32:05.815570   658 layer_factory.hpp:77] Creating layer ip2_ip2_0_split\n", "I0224 00:32:05.815579   658 net.cpp:106] Creating Layer ip2_ip2_0_split\n", "I0224 00:32:05.815587   658 net.cpp:454] ip2_ip2_0_split <- ip2\n", "I0224 00:32:05.815600   658 net.cpp:411] ip2_ip2_0_split -> ip2_ip2_0_split_0\n", "I0224 00:32:05.815619   658 net.cpp:411] ip2_ip2_0_split -> ip2_ip2_0_split_1\n", "I0224 00:32:05.815640   658 net.cpp:150] Setting up ip2_ip2_0_split\n", "I0224 00:32:05.815654   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.815662   658 net.cpp:157] Top shape: 10 2 (20)\n", "I0224 00:32:05.815670   658 net.cpp:165] Memory required for data: 3720\n", "I0224 00:32:05.815677   658 layer_factory.hpp:77] Creating layer accuracy\n", "I0224 00:32:05.815685   658 net.cpp:106] Creating Layer accuracy\n", "I0224 00:32:05.815693   658 net.cpp:454] accuracy <- ip2_ip2_0_split_0\n", "I0224 00:32:05.815702   658 net.cpp:454] accuracy <- label_data_1_split_0\n", "I0224 00:32:05.815711   658 net.cpp:411] accuracy -> accuracy\n", "I0224 00:32:05.815722   658 net.cpp:150] Setting up accuracy\n", "I0224 00:32:05.815732   658 net.cpp:157] Top shape: (1)\n", "I0224 00:32:05.815738   658 net.cpp:165] Memory required for data: 3724\n", "I0224 00:32:05.815747   658 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:05.815754   658 net.cpp:106] Creating Layer loss\n", "I0224 00:32:05.815762   658 net.cpp:454] loss <- ip2_ip2_0_split_1\n", "I0224 00:32:05.815770   658 net.cpp:454] loss <- label_data_1_split_1\n", "I0224 00:32:05.815779   658 net.cpp:411] loss -> loss\n", "I0224 00:32:05.815790   658 layer_factory.hpp:77] Creating layer loss\n", "I0224 00:32:05.815811   658 net.cpp:150] Setting up loss\n", "I0224 00:32:05.815829   658 net.cpp:157] Top shape: (1)\n", "I0224 00:32:05.815843   658 net.cpp:160]     with loss weight 1\n", "I0224 00:32:05.815867   658 net.cpp:165] Memory required for data: 3728\n", "I0224 00:32:05.815876   658 net.cpp:226] loss needs backward computation.\n", "I0224 00:32:05.815884   658 net.cpp:228] accuracy does not need backward computation.\n", "I0224 00:32:05.815892   658 net.cpp:226] ip2_ip2_0_split needs backward computation.\n", "I0224 00:32:05.815901   658 net.cpp:226] ip2 needs backward computation.\n", "I0224 00:32:05.815908   658 net.cpp:226] relu1 needs backward computation.\n", "I0224 00:32:05.815915   658 net.cpp:226] ip1 needs backward computation.\n", "I0224 00:32:05.815923   658 net.cpp:228] label_data_1_split does not need backward computation.\n", "I0224 00:32:05.815932   658 net.cpp:228] data does not need backward computation.\n", "I0224 00:32:05.815938   658 net.cpp:270] This network produces output accuracy\n", "I0224 00:32:05.815946   658 net.cpp:270] This network produces output loss\n", "I0224 00:32:05.815958   658 net.cpp:283] Network initialization done.\n", "I0224 00:32:05.815978   658 solver.cpp:60] Solver scaffolding done.\n", "I0224 00:32:05.816000   658 caffe.cpp:219] Starting Optimization\n", "I0224 00:32:05.816016   658 solver.cpp:280] Solving \n", "I0224 00:32:05.816030   658 solver.cpp:281] Learning Rate Policy: step\n", "I0224 00:32:05.816048   658 solver.cpp:338] Iteration 0, Testing net (#0)\n", "I0224 00:32:05.831967   658 solver.cpp:406]     Test net output #0: accuracy = 0.4464\n", "I0224 00:32:05.832033   658 solver.cpp:406]     Test net output #1: loss = 0.909841 (* 1 = 0.909841 loss)\n", "I0224 00:32:05.832186   658 solver.cpp:229] Iteration 0, loss = 0.798509\n", "I0224 00:32:05.832218   658 solver.cpp:245]     Train net output #0: accuracy = 0.6\n", "I0224 00:32:05.832247   658 solver.cpp:245]     Train net output #1: loss = 0.798509 (* 1 = 0.798509 loss)\n", "I0224 00:32:05.832281   658 sgd_solver.cpp:106] Iteration 0, lr = 0.01\n", "I0224 00:32:05.859506   658 solver.cpp:338] Iteration 1000, Testing net (#0)\n", "I0224 00:32:05.862799   658 solver.cpp:406]     Test net output #0: accuracy = 0.8156\n", "I0224 00:32:05.862818   658 solver.cpp:406]     Test net output #1: loss = 0.44259 (* 1 = 0.44259 loss)\n", "I0224 00:32:05.862853   658 solver.cpp:229] Iteration 1000, loss = 0.537015\n", "I0224 00:32:05.862864   658 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:05.862875   658 solver.cpp:245]     Train net output #1: loss = 0.537015 (* 1 = 0.537015 loss)\n", "I0224 00:32:05.862885   658 sgd_solver.cpp:106] Iteration 1000, lr = 0.01\n", "I0224 00:32:05.883155   658 solver.cpp:338] Iteration 2000, Testing net (#0)\n", "I0224 00:32:05.886435   658 solver.cpp:406]     Test net output #0: accuracy = 0.8116\n", "I0224 00:32:05.886451   658 solver.cpp:406]     Test net output #1: loss = 0.434079 (* 1 = 0.434079 loss)\n", "I0224 00:32:05.886484   658 solver.cpp:229] Iteration 2000, loss = 0.43109\n", "I0224 00:32:05.886497   658 solver.cpp:245]     Train net output #0: accuracy = 0.9\n", "I0224 00:32:05.886508   658 solver.cpp:245]     Train net output #1: loss = 0.43109 (* 1 = 0.43109 loss)\n", "I0224 00:32:05.886518   658 sgd_solver.cpp:106] Iteration 2000, lr = 0.01\n", "I0224 00:32:05.907243   658 solver.cpp:338] Iteration 3000, Testing net (#0)\n", "I0224 00:32:05.910521   658 solver.cpp:406]     Test net output #0: accuracy = 0.8168\n", "I0224 00:32:05.910537   658 solver.cpp:406]     Test net output #1: loss = 0.425661 (* 1 = 0.425661 loss)\n", "I0224 00:32:05.910905   658 solver.cpp:229] Iteration 3000, loss = 0.430245\n", "I0224 00:32:05.910922   658 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:05.910933   658 solver.cpp:245]     Train net output #1: loss = 0.430245 (* 1 = 0.430245 loss)\n", "I0224 00:32:05.910943   658 sgd_solver.cpp:106] Iteration 3000, lr = 0.01\n", "I0224 00:32:05.931205   658 solver.cpp:338] Iteration 4000, Testing net (#0)\n", "I0224 00:32:05.934479   658 solver.cpp:406]     Test net output #0: accuracy = 0.8324\n", "I0224 00:32:05.934496   658 solver.cpp:406]     Test net output #1: loss = 0.404891 (* 1 = 0.404891 loss)\n", "I0224 00:32:05.934530   658 solver.cpp:229] Iteration 4000, loss = 0.628955\n", "I0224 00:32:05.934542   658 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:05.934553   658 solver.cpp:245]     Train net output #1: loss = 0.628955 (* 1 = 0.628955 loss)\n", "I0224 00:32:05.934583   658 sgd_solver.cpp:106] Iteration 4000, lr = 0.01\n", "I0224 00:32:05.955108   658 solver.cpp:338] Iteration 5000, Testing net (#0)\n", "I0224 00:32:05.958377   658 solver.cpp:406]     Test net output #0: accuracy = 0.8364\n", "I0224 00:32:05.958395   658 solver.cpp:406]     Test net output #1: loss = 0.404235 (* 1 = 0.404235 loss)\n", "I0224 00:32:05.958432   658 solver.cpp:229] Iteration 5000, loss = 0.394939\n", "I0224 00:32:05.958444   658 solver.cpp:245]     Train net output #0: accuracy = 0.9\n", "I0224 00:32:05.958456   658 solver.cpp:245]     Train net output #1: loss = 0.39494 (* 1 = 0.39494 loss)\n", "I0224 00:32:05.958466   658 sgd_solver.cpp:106] Iteration 5000, lr = 0.001\n", "I0224 00:32:05.978703   658 solver.cpp:338] Iteration 6000, Testing net (#0)\n", "I0224 00:32:05.981973   658 solver.cpp:406]     Test net output #0: accuracy = 0.838\n", "I0224 00:32:05.981991   658 solver.cpp:406]     Test net output #1: loss = 0.385743 (* 1 = 0.385743 loss)\n", "I0224 00:32:05.982347   658 solver.cpp:229] Iteration 6000, loss = 0.411537\n", "I0224 00:32:05.982362   658 solver.cpp:245]     Train net output #0: accuracy = 0.8\n", "I0224 00:32:05.982373   658 solver.cpp:245]     Train net output #1: loss = 0.411537 (* 1 = 0.411537 loss)\n", "I0224 00:32:05.982383   658 sgd_solver.cpp:106] Iteration 6000, lr = 0.001\n", "I0224 00:32:06.003015   658 solver.cpp:338] Iteration 7000, Testing net (#0)\n", "I0224 00:32:06.006283   658 solver.cpp:406]     Test net output #0: accuracy = 0.8388\n", "I0224 00:32:06.006301   658 solver.cpp:406]     Test net output #1: loss = 0.384648 (* 1 = 0.384648 loss)\n", "I0224 00:32:06.006335   658 solver.cpp:229] Iteration 7000, loss = 0.521072\n", "I0224 00:32:06.006347   658 solver.cpp:245]     Train net output #0: accuracy = 0.7\n", "I0224 00:32:06.006358   658 solver.cpp:245]     Train net output #1: loss = 0.521073 (* 1 = 0.521073 loss)\n", "I0224 00:32:06.006368   658 sgd_solver.cpp:106] Iteration 7000, lr = 0.001\n", "I0224 00:32:06.026715   658 solver.cpp:338] Iteration 8000, Testing net (#0)\n", "I0224 00:32:06.029965   658 solver.cpp:406]     Test net output #0: accuracy = 0.8404\n", "I0224 00:32:06.029983   658 solver.cpp:406]     Test net output #1: loss = 0.380889 (* 1 = 0.380889 loss)\n", "I0224 00:32:06.030015   658 solver.cpp:229] Iteration 8000, loss = 0.329477\n", "I0224 00:32:06.030028   658 solver.cpp:245]     Train net output #0: accuracy = 0.9\n", "I0224 00:32:06.030040   658 solver.cpp:245]     Train net output #1: loss = 0.329477 (* 1 = 0.329477 loss)\n", "I0224 00:32:06.030048   658 sgd_solver.cpp:106] Iteration 8000, lr = 0.001\n", "I0224 00:32:06.050626   658 solver.cpp:338] Iteration 9000, Testing net (#0)\n", "I0224 00:32:06.053889   658 solver.cpp:406]     Test net output #0: accuracy = 0.8376\n", "I0224 00:32:06.053906   658 solver.cpp:406]     Test net output #1: loss = 0.382756 (* 1 = 0.382756 loss)\n", "I0224 00:32:06.054271   658 solver.cpp:229] Iteration 9000, loss = 0.412227\n", "I0224 00:32:06.054291   658 solver.cpp:245]     Train net output #0: accuracy = 0.8\n", "I0224 00:32:06.054314   658 solver.cpp:245]     Train net output #1: loss = 0.412228 (* 1 = 0.412228 loss)\n", "I0224 00:32:06.054337   658 sgd_solver.cpp:106] Iteration 9000, lr = 0.001\n", "I0224 00:32:06.074646   658 solver.cpp:456] Snapshotting to binary proto file examples/hdf5_classification/data/train_iter_10000.caffemodel\n", "I0224 00:32:06.074808   658 sgd_solver.cpp:273] Snapshotting solver state to binary proto file examples/hdf5_classification/data/train_iter_10000.solverstate\n", "I0224 00:32:06.074889   658 solver.cpp:318] Iteration 10000, loss = 0.532798\n", "I0224 00:32:06.074906   658 solver.cpp:338] Iteration 10000, Testing net (#0)\n", "I0224 00:32:06.078208   658 solver.cpp:406]     Test net output #0: accuracy = 0.8388\n", "I0224 00:32:06.078225   658 solver.cpp:406]     Test net output #1: loss = 0.382042 (* 1 = 0.382042 loss)\n", "I0224 00:32:06.078234   658 solver.cpp:323] Optimization Done.\n", "I0224 00:32:06.078241   658 caffe.cpp:222] Optimization Done.\n"]}], "source": ["!./build/tools/caffe train -solver examples/hdf5_classification/nonlinear_logreg_solver.prototxt"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false}, "outputs": [], "source": ["# Clean up (comment this out if you want to examine the hdf5_classification/data directory).\n", "shutil.rmtree(dirname)"]}], "metadata": {"description": "Use Caffe as a generic SGD optimizer to train logistic regression on non-image HDF5 data.", "example_name": "Off-the-shelf SGD for classification", "include_in_docs": true, "kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.10"}, "priority": 4}, "nbformat": 4, "nbformat_minor": 0}