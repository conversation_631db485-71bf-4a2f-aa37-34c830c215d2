# Config file for the <PERSON>affe package.
#
# Note:
#   Caffe and this config file depends on opencv,
#   so put `find_package(OpenCV)` before searching Caffe
#   via `find_package(Caffe)`. All other lib/includes
#   dependencies are hard coded in the file
#
# After successful configuration the following variables
# will be defined:
#
#   Caffe_LIBRARIES    - IMPORTED targets to link against
#                        (There is no Caffe_INCLUDE_DIRS and Caffe_DEFINITIONS
#                         because they are specified in the IMPORTED target interface.)
#
#   Caffe_HAVE_CUDA    - signals about CUDA support
#   Caffe_HAVE_CUDNN   - signals about cuDNN support


# OpenCV dependency (optional)

if(@USE_OPENCV@)
  if(NOT OpenCV_FOUND)
    set(Caffe_OpenCV_CONFIG_PATH "@OpenCV_CONFIG_PATH@")
    if(Caf<PERSON>_OpenCV_CONFIG_PATH)
      get_filename_component(Caffe_OpenCV_CONFIG_PATH ${Caffe_OpenCV_CONFIG_PATH} ABSOLUTE)

      if(EXISTS ${Caffe_OpenCV_CONFIG_PATH} AND NOT TARGET opencv_core)
        message(STATUS "Caffe: using OpenCV config from ${Caffe_OpenCV_CONFIG_PATH}")
	include(${Caffe_OpenCV_CONFIG_PATH}/OpenCVConfig.cmake)
      endif()

    else()
      find_package(OpenCV REQUIRED)
    endif()
    unset(Caffe_OpenCV_CONFIG_PATH)
  endif()
endif()

# Compute paths
get_filename_component(Caffe_CMAKE_DIR "${CMAKE_CURRENT_LIST_FILE}" PATH)

# Our library dependencies
if(NOT TARGET caffe AND NOT caffe_BINARY_DIR)
  include("${Caffe_CMAKE_DIR}/CaffeTargets.cmake")
endif()

# List of IMPORTED libs created by CaffeTargets.cmake
# These targets already specify all needed definitions and include pathes
set(Caffe_LIBRARIES caffe)

# Cuda support variables
set(Caffe_CPU_ONLY @CPU_ONLY@)
set(Caffe_HAVE_CUDA @HAVE_CUDA@)
set(Caffe_HAVE_CUDNN @HAVE_CUDNN@)
