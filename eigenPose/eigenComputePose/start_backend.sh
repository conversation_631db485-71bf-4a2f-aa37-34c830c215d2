#!/bin/bash

# Eigen Compute Pose Backend Startup Script
# This script sets up the environment and starts the FastAPI backend

echo "🚀 Starting Eigen Compute Pose Backend..."

# Check if we're in the correct directory
if [ ! -f "api/main.py" ]; then
    echo "❌ Error: Please run this script from the eigenComputePose directory"
    exit 1
fi

# Load environment variables if .env file exists
if [ -f ".env" ]; then
    echo "📄 Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  No .env file found. Using default configuration."
    echo "💡 Copy .env.example to .env and configure your settings."
fi

# Set default values if not provided
export BACKEND_API_HOST=${BACKEND_API_HOST:-"0.0.0.0"}
export BACKEND_API_PORT=${BACKEND_API_PORT:-8000}
export BACKEND_API_RELOAD=${BACKEND_API_RELOAD:-false}
export BACKEND_API_WORKERS=${BACKEND_API_WORKERS:-1}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Check database connection
if [ -n "$DATABASE_URL" ]; then
    echo "🗄️  Database URL configured: ${DATABASE_URL:0:20}..."
else
    echo "⚠️  No DATABASE_URL configured. Database features will be disabled."
fi

# Setup OpenPose environment
echo "🦴 Setting up OpenPose environment..."
python -c "
import sys
sys.path.append('.')
from config import setup_openpose_environment
setup_openpose_environment()
print('✅ OpenPose environment configured')
"

# Check if OpenPose is available
echo "🔍 Checking OpenPose availability..."
python -c "
try:
    import sys
    sys.path.append('.')
    from config import setup_openpose_environment
    setup_openpose_environment()
    import openpose
    print('✅ OpenPose is available')
except ImportError as e:
    print(f'⚠️  OpenPose not available: {e}')
    print('   The backend will start but pose detection will be disabled.')
except Exception as e:
    print(f'⚠️  OpenPose check failed: {e}')
"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p inputVideos
mkdir -p outputResults
mkdir -p temp
mkdir -p logs

# Start the server
echo "🌐 Starting FastAPI server..."
echo "   Host: $BACKEND_API_HOST"
echo "   Port: $BACKEND_API_PORT"
echo "   Workers: $BACKEND_API_WORKERS"
echo "   Reload: $BACKEND_API_RELOAD"
echo ""
echo "🔗 API will be available at: http://$BACKEND_API_HOST:$BACKEND_API_PORT"
echo "📚 API documentation: http://$BACKEND_API_HOST:$BACKEND_API_PORT/docs"
echo ""

# Start with uvicorn
if [ "$BACKEND_API_RELOAD" = "true" ]; then
    uvicorn api.main:app \
        --host $BACKEND_API_HOST \
        --port $BACKEND_API_PORT \
        --reload
else
    uvicorn api.main:app \
        --host $BACKEND_API_HOST \
        --port $BACKEND_API_PORT \
        --workers $BACKEND_API_WORKERS
fi
