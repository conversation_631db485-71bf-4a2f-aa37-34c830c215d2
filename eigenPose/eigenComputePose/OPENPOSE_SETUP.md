# OpenPose Setup Guide

This document explains how to set up OpenPose after moving the eigenPose folder structure.

## Problem

After moving the eigenPose folder into the eigenCompute directory, OpenPose could not find the required shared libraries:
- `libopenpose.so.1.7.0`
- `libcaffe.so.1.0.0`

## Solution

The issue was resolved by properly setting up the library paths in the `LD_LIBRARY_PATH` environment variable.

### Files Modified

1. **config.py** - Added library path configurations and setup function
2. **test_setup.py** - Updated to use relative paths and set up library paths
3. **modules/video_processor.py** - Updated to use the configuration setup function
4. **setup_environment.sh** - Created a script to set up the environment

### Key Changes

#### 1. Added Library Path Constants in config.py
```python
OPENPOSE_LIB_PATH = OPENPOSE_ROOT / "build" / "src" / "openpose"
CAFFE_LIB_PATH = OPENPOSE_ROOT / "build" / "caffe" / "lib"
```

#### 2. Created setup_openpose_environment() Function
This function automatically sets up the necessary environment variables:
- Adds OpenPose Python path to `sys.path`
- Adds OpenPose library path to `LD_LIBRARY_PATH`
- Adds Caffe library path to `LD_LIBRARY_PATH`

#### 3. Environment Setup Script
Created `setup_environment.sh` that can be sourced to set up the environment:
```bash
source setup_environment.sh
```

## Usage

### Method 1: Using the Setup Script (Recommended)
```bash
cd eigenPose/eigenComputePose
source setup_environment.sh
python3 test_setup.py
```

### Method 2: Using Python Configuration
In your Python code:
```python
from config import setup_openpose_environment
setup_openpose_environment()
from openpose import pyopenpose as op
```

## Directory Structure

The current structure after the move:
```
eigenCompute/
├── eigenPose/
│   ├── openpose/
│   │   ├── build/
│   │   │   ├── src/openpose/          # Contains libopenpose.so.1.7.0
│   │   │   ├── caffe/lib/             # Contains libcaffe.so.1.0.0
│   │   │   └── python/                # Contains Python bindings
│   │   └── models/                    # OpenPose models
│   └── eigenComputePose/              # Main application
│       ├── config.py
│       ├── test_setup.py
│       ├── setup_environment.sh
│       └── modules/
└── eigenFrontend/
```

## Verification

Run the test script to verify everything is working:
```bash
source setup_environment.sh
python3 test_setup.py
```

All tests should pass:
- ✅ Dependencies
- ✅ Imports  
- ✅ Configuration
- ✅ GPU
- ✅ OpenPose
- ✅ Basic Functionality

## Troubleshooting

If you encounter issues:

1. **Check library files exist:**
   ```bash
   ls -la ../openpose/build/src/openpose/libopenpose.so.1.7.0
   ls -la ../openpose/build/caffe/lib/libcaffe.so.1.0.0
   ```

2. **Check environment variables:**
   ```bash
   echo $LD_LIBRARY_PATH
   echo $PYTHONPATH
   ```

3. **Run the setup script with verbose output:**
   ```bash
   bash -x setup_environment.sh
   ```

## Notes

- The setup is now portable and uses relative paths
- No hardcoded absolute paths remain in the code
- The environment setup is automatic when using the configuration functions
- The setup script provides detailed feedback about what paths are being set
