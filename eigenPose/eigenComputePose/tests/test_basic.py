"""
Basic tests for gait analysis platform.
"""
import pytest
import numpy as np
from pathlib import Path
import sys

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config import get_config
from modules.utils import (
    euclidean_distance, 
    calculate_angle, 
    estimate_body_height,
    smooth_keypoints
)
from modules.biomechanical_analyzer import BiomechanicalAnalyzer

class TestUtils:
    """Test utility functions."""
    
    def test_euclidean_distance(self):
        """Test Euclidean distance calculation."""
        p1 = (0, 0)
        p2 = (3, 4)
        distance = euclidean_distance(p1, p2)
        assert abs(distance - 5.0) < 1e-6
    
    def test_calculate_angle(self):
        """Test angle calculation."""
        # Right angle test
        p1 = (0, 1)
        p2 = (0, 0)  # vertex
        p3 = (1, 0)
        angle = calculate_angle(p1, p2, p3)
        assert abs(angle - 90.0) < 1e-6
    
    def test_estimate_body_height(self):
        """Test body height estimation."""
        # Create mock keypoints (25 keypoints with x, y, confidence)
        keypoints = np.random.rand(25, 3)
        keypoints[:, 2] = 0.8  # Set confidence
        
        # Set specific keypoints for hip and ankle
        keypoints[12, :2] = [100, 200]  # LHip
        keypoints[9, :2] = [120, 200]   # RHip
        keypoints[14, :2] = [105, 400]  # LAnkle
        keypoints[11, :2] = [115, 400]  # RAnkle
        
        height = estimate_body_height(keypoints)
        assert height > 0
    
    def test_smooth_keypoints(self):
        """Test keypoint smoothing."""
        # Create sequence of keypoints with noise
        sequence = []
        for i in range(10):
            keypoints = np.random.rand(25, 3)
            sequence.append(keypoints)
        
        smoothed = smooth_keypoints(sequence, window_size=3)
        assert len(smoothed) == len(sequence)

class TestBiomechanicalAnalyzer:
    """Test biomechanical analyzer."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = get_config()
        self.analyzer = BiomechanicalAnalyzer(self.config)
    
    def test_initialization(self):
        """Test analyzer initialization."""
        assert self.analyzer is not None
        assert self.analyzer.keypoint_indices is not None
    
    def test_posterior_stability_score(self):
        """Test posterior stability score calculation."""
        measurements = {
            "footDrift": 10.0,
            "kneeDrift": 1.2,
            "heelWhip": 5.0,
            "pelvicDrop": 2.0
        }
        
        score = self.analyzer.calculate_posterior_stability_score(measurements)
        assert isinstance(score, float)
        assert score >= 0

class TestConfiguration:
    """Test configuration loading."""
    
    def test_config_loading(self):
        """Test that configuration loads correctly."""
        config = get_config()
        
        assert "openpose" in config
        assert "video" in config
        assert "gait" in config
        assert "keypoints" in config
        
        # Test keypoint indices
        keypoints = config["keypoints"]
        assert "LHip" in keypoints
        assert "RHip" in keypoints
        assert "LKnee" in keypoints
        assert "RKnee" in keypoints

if __name__ == "__main__":
    pytest.main([__file__])
