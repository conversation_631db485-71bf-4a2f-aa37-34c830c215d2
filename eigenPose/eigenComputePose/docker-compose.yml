version: '3.8'

services:
  gait-analysis:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gait-analysis-platform
    ports:
      - "8000:8000"
    volumes:
      # Mount input and output directories
      - ./inputVideos:/app/inputVideos
      - ./outputResults:/app/outputResults
      - ./logs:/app/logs
      # Mount OpenPose models (if needed)
      - ../openpose/models:/app/openpose/models:ro
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    runtime: nvidia
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Optional: Add a reverse proxy (nginx)
  nginx:
    image: nginx:alpine
    container_name: gait-analysis-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates if needed
    depends_on:
      - gait-analysis
    restart: unless-stopped

volumes:
  input_videos:
  output_results:
  logs:
