"""
Database integration module for storing analysis results.
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and operations for storing analysis results."""
    
    def __init__(self):
        """Initialize database connection."""
        self.connection = None
        self.connect()
    
    def connect(self):
        """Establish database connection using environment variables."""
        try:
            # Get database URL from environment
            database_url = os.getenv('DATABASE_URL') or os.getenv('DIRECT_URL')
            
            if not database_url:
                logger.error("No database URL found in environment variables")
                return False
            
            self.connection = psycopg2.connect(
                database_url,
                cursor_factory=RealDictCursor
            )
            logger.info("Successfully connected to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            self.connection = None
            return False
    
    def disconnect(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def update_running_profile_analysis(
        self, 
        profile_id: str, 
        analysis_results: Dict[str, Any],
        video_type: str = "sagittal"
    ) -> bool:
        """
        Update running profile with analysis results.
        
        Args:
            profile_id: The running profile ID
            analysis_results: The complete analysis results from video processing
            video_type: Type of video analyzed (sagittal or posterior)
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.connection:
            logger.error("No database connection available")
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # Extract biomechanical measurements
            measurements = analysis_results.get('biomechanical_measurements', {})
            
            # Map analysis results to database fields
            # Note: Only updating fields that exist in the current schema
            update_fields = {}
            
            # Video analysis fields that exist in the schema
            if 'pelvicDrop' in measurements:
                update_fields['pelvicDrop'] = float(measurements['pelvicDrop'])
            
            if 'kneeDrift' in measurements:
                update_fields['kneeDrift'] = float(measurements['kneeDrift'])
            
            if 'footDrift' in measurements:
                update_fields['footDrift'] = float(measurements['footDrift'])
            
            if 'heelWhip' in measurements:
                update_fields['heelWhip'] = float(measurements['heelWhip'])
            
            if 'posteriorStabilityScore' in measurements:
                update_fields['posteriorStabilityScore'] = float(measurements['posteriorStabilityScore'])
            
            if 'overstride' in measurements:
                update_fields['overstride'] = float(measurements['overstride'])
            
            if 'ankleDorsiflexion' in measurements:
                update_fields['ankleDorsiflexion'] = float(measurements['ankleDorsiflexion'])
            
            if 'anklePlantarflexion' in measurements:
                update_fields['anklePlantarflexion'] = float(measurements['anklePlantarflexion'])
            
            if 'verticalOscillationVideo' in measurements:
                update_fields['verticalOscillationVideo'] = float(measurements['verticalOscillationVideo'])
            
            if 'trunkLean' in measurements:
                update_fields['trunkLean'] = float(measurements['trunkLean'])
            
            if 'kneeFlexionLoading' in measurements:
                update_fields['kneeFlexionLoading'] = float(measurements['kneeFlexionLoading'])
            
            # Add cadence if available
            if 'Cadence' in measurements:
                # Convert from steps/second to steps/minute if needed
                cadence_value = float(measurements['Cadence'])
                if cadence_value < 10:  # Likely in steps/second, convert to steps/minute
                    cadence_value *= 60
                update_fields['averageCadence'] = int(cadence_value)
            
            if not update_fields:
                logger.warning("No valid fields to update in database")
                return False
            
            # Build the UPDATE query dynamically
            set_clause = sql.SQL(', ').join([
                sql.SQL('{} = %s').format(sql.Identifier(field))
                for field in update_fields.keys()
            ])
            
            query = sql.SQL("""
                UPDATE "RunningProfile" 
                SET {set_clause}, "updatedAt" = %s
                WHERE id = %s
            """).format(set_clause=set_clause)
            
            # Prepare values for the query
            values = list(update_fields.values()) + [datetime.now(), profile_id]
            
            cursor.execute(query, values)
            self.connection.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"Successfully updated profile {profile_id} with analysis results")
                return True
            else:
                logger.warning(f"No profile found with ID {profile_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update running profile: {e}")
            if self.connection:
                self.connection.rollback()
            return False
        finally:
            if cursor:
                cursor.close()
    
    def log_analysis_completion(
        self, 
        profile_id: str, 
        user_id: str, 
        video_type: str,
        success: bool,
        error_message: Optional[str] = None
    ) -> bool:
        """
        Log analysis completion for tracking purposes.
        
        Args:
            profile_id: The running profile ID
            user_id: The user ID
            video_type: Type of video analyzed
            success: Whether analysis was successful
            error_message: Error message if analysis failed
            
        Returns:
            bool: True if logged successfully
        """
        try:
            # For now, just log to application logs
            # In the future, this could be stored in a dedicated analysis_logs table
            status = "SUCCESS" if success else "FAILED"
            log_message = f"Analysis {status} - Profile: {profile_id}, User: {user_id}, Video: {video_type}"
            
            if error_message:
                log_message += f", Error: {error_message}"
            
            logger.info(log_message)
            return True
            
        except Exception as e:
            logger.error(f"Failed to log analysis completion: {e}")
            return False

# Global database manager instance
db_manager = None

def get_database_manager() -> Optional[DatabaseManager]:
    """Get or create database manager instance."""
    global db_manager
    
    if db_manager is None:
        db_manager = DatabaseManager()
    
    return db_manager if db_manager.connection else None

def cleanup_database_manager():
    """Cleanup database manager on shutdown."""
    global db_manager
    
    if db_manager:
        db_manager.disconnect()
        db_manager = None
