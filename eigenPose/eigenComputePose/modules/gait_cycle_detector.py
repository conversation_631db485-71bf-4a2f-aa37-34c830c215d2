"""
Gait cycle detection module for identifying running gait phases.
"""
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import logging
from scipy import signal
from scipy.ndimage import gaussian_filter1d

from .utils import (
    euclidean_distance,
    filter_low_confidence_keypoints,
    smooth_keypoints,
    apply_butterworth_filter
)

logger = logging.getLogger(__name__)

class GaitCycleDetector:
    """
    Detector for identifying gait cycles and phases in running videos.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize gait cycle detector.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.gait_config = config.get("gait", {})
        self.keypoint_indices = config.get("keypoints", {})
        
        # Gait detection parameters
        self.min_confidence = self.gait_config.get("min_confidence", 0.3)
        self.smoothing_window = self.gait_config.get("smoothing_window", 5)
        self.velocity_threshold = self.gait_config.get("velocity_threshold", 0.1)
        self.height_threshold = self.gait_config.get("height_threshold", 0.02)
        self.min_cycle_frames = self.gait_config.get("gait_cycle_min_frames", 20)
        self.max_cycle_frames = self.gait_config.get("gait_cycle_max_frames", 120)
    
    def detect_gait_cycles(self, keypoints_sequence: List[np.ndarray], fps: float) -> Dict[str, Any]:
        """
        Detect gait cycles from keypoint sequence.
        
        Args:
            keypoints_sequence: List of keypoint arrays for each frame
            fps: Video frame rate
            
        Returns:
            Dictionary containing gait cycle information
        """
        logger.info("Detecting gait cycles...")
        
        if len(keypoints_sequence) < self.min_cycle_frames:
            logger.warning("Insufficient frames for gait cycle detection")
            return {"cycles": [], "phases": [], "events": []}
        
        # Filter and smooth keypoints
        filtered_keypoints = self._preprocess_keypoints(keypoints_sequence)
        
        # Detect initial contact events
        ic_events = self._detect_initial_contact_events(filtered_keypoints, fps)
        
        # Detect toe-off events
        to_events = self._detect_toe_off_events(filtered_keypoints, fps)
        
        # Identify complete gait cycles
        gait_cycles = self._identify_gait_cycles(ic_events, to_events, len(keypoints_sequence))
        
        # Detect gait phases within cycles
        gait_phases = self._detect_gait_phases(gait_cycles, ic_events, to_events)
        
        # Combine all events
        all_events = self._combine_events(ic_events, to_events)
        
        results = {
            "cycles": gait_cycles,
            "phases": gait_phases,
            "events": all_events,
            "ic_events": ic_events,
            "to_events": to_events,
            "total_cycles": len(gait_cycles),
            "fps": fps,
        }
        
        logger.info(f"Detected {len(gait_cycles)} complete gait cycles")
        return results
    
    def _preprocess_keypoints(self, keypoints_sequence: List[np.ndarray]) -> List[np.ndarray]:
        """Preprocess keypoints by filtering and smoothing."""
        # Filter low confidence keypoints
        filtered = []
        for keypoints in keypoints_sequence:
            filtered_kp = filter_low_confidence_keypoints(keypoints, self.min_confidence)
            filtered.append(filtered_kp)
        
        # Apply smoothing
        smoothed = smooth_keypoints(filtered, self.smoothing_window)
        
        return smoothed
    
    def _detect_initial_contact_events(self, keypoints_sequence: List[np.ndarray], fps: float) -> List[Dict[str, Any]]:
        """
        Detect initial contact (IC) events using multiple methods.
        
        Primary method: Biggest vertical deceleration of large toe
        Secondary method: Foot velocity near zero while on ground
        """
        ic_events = []
        
        # Extract foot keypoints
        left_toe_positions = self._extract_keypoint_positions(keypoints_sequence, "LBigToe")
        right_toe_positions = self._extract_keypoint_positions(keypoints_sequence, "RBigToe")
        left_heel_positions = self._extract_keypoint_positions(keypoints_sequence, "LHeel")
        right_heel_positions = self._extract_keypoint_positions(keypoints_sequence, "RHeel")
        
        # Method 1: Vertical deceleration of big toe
        left_ic_events = self._detect_ic_by_toe_deceleration(left_toe_positions, fps, "left")
        right_ic_events = self._detect_ic_by_toe_deceleration(right_toe_positions, fps, "right")
        
        # Method 2: Foot velocity analysis
        left_ic_velocity = self._detect_ic_by_velocity(left_heel_positions, fps, "left")
        right_ic_velocity = self._detect_ic_by_velocity(right_heel_positions, fps, "right")
        
        # Combine and validate events
        all_ic_candidates = left_ic_events + right_ic_events + left_ic_velocity + right_ic_velocity
        
        # Sort by frame and remove duplicates
        all_ic_candidates.sort(key=lambda x: x["frame"])
        ic_events = self._remove_duplicate_events(all_ic_candidates, min_separation=int(fps * 0.2))
        
        return ic_events
    
    def _detect_ic_by_toe_deceleration(self, toe_positions: List[Tuple[float, float]], 
                                     fps: float, foot: str) -> List[Dict[str, Any]]:
        """Detect IC events by finding biggest vertical deceleration of toe."""
        if len(toe_positions) < 10:
            return []
        
        # Extract y-coordinates (vertical positions)
        y_positions = [pos[1] for pos in toe_positions if not np.isnan(pos[1])]
        if len(y_positions) < 10:
            return []
        
        # Calculate velocity and acceleration
        dt = 1.0 / fps
        velocity = np.gradient(y_positions, dt)
        acceleration = np.gradient(velocity, dt)
        
        # Apply smoothing to reduce noise
        acceleration_smooth = gaussian_filter1d(acceleration, sigma=1.0)
        
        # Find peaks in negative acceleration (deceleration)
        # In image coordinates, y increases downward, so negative acceleration means upward deceleration
        deceleration = -acceleration_smooth
        
        # Find significant deceleration peaks
        peaks, properties = signal.find_peaks(
            deceleration,
            height=np.std(deceleration) * 1.5,  # Threshold based on standard deviation
            distance=int(fps * 0.3),  # Minimum 0.3 seconds between peaks
            prominence=np.std(deceleration) * 0.5
        )
        
        ic_events = []
        for peak_idx in peaks:
            if 0 <= peak_idx < len(toe_positions):
                ic_events.append({
                    "frame": peak_idx,
                    "timestamp": peak_idx / fps,
                    "foot": foot,
                    "event_type": "initial_contact",
                    "method": "toe_deceleration",
                    "confidence": float(deceleration[peak_idx]),
                    "position": toe_positions[peak_idx]
                })
        
        return ic_events
    
    def _detect_ic_by_velocity(self, heel_positions: List[Tuple[float, float]], 
                             fps: float, foot: str) -> List[Dict[str, Any]]:
        """Detect IC events by finding when foot velocity approaches zero."""
        if len(heel_positions) < 10:
            return []
        
        # Calculate foot velocity magnitude
        velocities = []
        for i in range(1, len(heel_positions)):
            if not (np.isnan(heel_positions[i-1]).any() or np.isnan(heel_positions[i]).any()):
                dx = heel_positions[i][0] - heel_positions[i-1][0]
                dy = heel_positions[i][1] - heel_positions[i-1][1]
                velocity_mag = np.sqrt(dx**2 + dy**2) * fps
                velocities.append(velocity_mag)
            else:
                velocities.append(np.nan)
        
        if len(velocities) < 5:
            return []
        
        # Apply smoothing
        valid_velocities = [v for v in velocities if not np.isnan(v)]
        if not valid_velocities:
            return []
        
        velocity_threshold = np.mean(valid_velocities) * self.velocity_threshold
        
        # Find frames where velocity is below threshold
        low_velocity_frames = []
        for i, vel in enumerate(velocities):
            if not np.isnan(vel) and vel < velocity_threshold:
                low_velocity_frames.append(i + 1)  # +1 because velocity array is offset
        
        # Group consecutive low velocity frames and find the start of each group
        ic_events = []
        if low_velocity_frames:
            groups = []
            current_group = [low_velocity_frames[0]]
            
            for frame in low_velocity_frames[1:]:
                if frame - current_group[-1] <= 3:  # Allow small gaps
                    current_group.append(frame)
                else:
                    groups.append(current_group)
                    current_group = [frame]
            groups.append(current_group)
            
            # Take the first frame of each group as IC event
            for group in groups:
                if len(group) >= 3:  # Require at least 3 consecutive frames
                    frame_idx = group[0]
                    if 0 <= frame_idx < len(heel_positions):
                        ic_events.append({
                            "frame": frame_idx,
                            "timestamp": frame_idx / fps,
                            "foot": foot,
                            "event_type": "initial_contact",
                            "method": "velocity_threshold",
                            "confidence": 1.0 - (velocities[frame_idx-1] / np.mean(valid_velocities)),
                            "position": heel_positions[frame_idx]
                        })
        
        return ic_events
    
    def _detect_toe_off_events(self, keypoints_sequence: List[np.ndarray], fps: float) -> List[Dict[str, Any]]:
        """Detect toe-off (TO) events."""
        to_events = []
        
        # Extract toe positions
        left_toe_positions = self._extract_keypoint_positions(keypoints_sequence, "LBigToe")
        right_toe_positions = self._extract_keypoint_positions(keypoints_sequence, "RBigToe")
        
        # Detect TO by sudden upward movement of toe
        left_to_events = self._detect_to_by_toe_elevation(left_toe_positions, fps, "left")
        right_to_events = self._detect_to_by_toe_elevation(right_toe_positions, fps, "right")
        
        # Combine events
        all_to_candidates = left_to_events + right_to_events
        all_to_candidates.sort(key=lambda x: x["frame"])
        to_events = self._remove_duplicate_events(all_to_candidates, min_separation=int(fps * 0.2))
        
        return to_events
    
    def _detect_to_by_toe_elevation(self, toe_positions: List[Tuple[float, float]], 
                                  fps: float, foot: str) -> List[Dict[str, Any]]:
        """Detect TO events by sudden upward movement of toe."""
        if len(toe_positions) < 10:
            return []
        
        # Extract y-coordinates
        y_positions = [pos[1] for pos in toe_positions if not np.isnan(pos[1])]
        if len(y_positions) < 10:
            return []
        
        # Calculate velocity (negative because y increases downward)
        dt = 1.0 / fps
        velocity = -np.gradient(y_positions, dt)  # Negative for upward movement
        
        # Apply smoothing
        velocity_smooth = gaussian_filter1d(velocity, sigma=1.0)
        
        # Find peaks in upward velocity
        peaks, properties = signal.find_peaks(
            velocity_smooth,
            height=np.std(velocity_smooth) * 1.0,
            distance=int(fps * 0.3),
            prominence=np.std(velocity_smooth) * 0.3
        )
        
        to_events = []
        for peak_idx in peaks:
            if 0 <= peak_idx < len(toe_positions):
                to_events.append({
                    "frame": peak_idx,
                    "timestamp": peak_idx / fps,
                    "foot": foot,
                    "event_type": "toe_off",
                    "method": "toe_elevation",
                    "confidence": float(velocity_smooth[peak_idx]),
                    "position": toe_positions[peak_idx]
                })
        
        return to_events
    
    def _extract_keypoint_positions(self, keypoints_sequence: List[np.ndarray], 
                                  keypoint_name: str) -> List[Tuple[float, float]]:
        """Extract positions for a specific keypoint across all frames."""
        keypoint_idx = self.keypoint_indices.get(keypoint_name)
        if keypoint_idx is None:
            logger.warning(f"Unknown keypoint: {keypoint_name}")
            return []
        
        positions = []
        for keypoints in keypoints_sequence:
            if keypoint_idx < len(keypoints):
                x, y = keypoints[keypoint_idx, :2]
                positions.append((float(x), float(y)))
            else:
                positions.append((np.nan, np.nan))
        
        return positions
    
    def _remove_duplicate_events(self, events: List[Dict[str, Any]], min_separation: int) -> List[Dict[str, Any]]:
        """Remove duplicate events that are too close in time."""
        if not events:
            return []
        
        filtered_events = [events[0]]
        
        for event in events[1:]:
            last_event = filtered_events[-1]
            if event["frame"] - last_event["frame"] >= min_separation:
                filtered_events.append(event)
            elif event.get("confidence", 0) > last_event.get("confidence", 0):
                # Replace with higher confidence event
                filtered_events[-1] = event
        
        return filtered_events
    
    def _identify_gait_cycles(self, ic_events: List[Dict[str, Any]], 
                            to_events: List[Dict[str, Any]], total_frames: int) -> List[Dict[str, Any]]:
        """Identify complete gait cycles from IC and TO events."""
        gait_cycles = []
        
        # Group events by foot
        left_ic = [e for e in ic_events if e["foot"] == "left"]
        right_ic = [e for e in ic_events if e["foot"] == "right"]
        
        # Find cycles for each foot
        for foot, ic_list in [("left", left_ic), ("right", right_ic)]:
            for i in range(len(ic_list) - 1):
                start_frame = ic_list[i]["frame"]
                end_frame = ic_list[i + 1]["frame"]
                cycle_length = end_frame - start_frame
                
                # Validate cycle length
                if self.min_cycle_frames <= cycle_length <= self.max_cycle_frames:
                    gait_cycles.append({
                        "foot": foot,
                        "start_frame": start_frame,
                        "end_frame": end_frame,
                        "duration_frames": cycle_length,
                        "start_timestamp": ic_list[i]["timestamp"],
                        "end_timestamp": ic_list[i + 1]["timestamp"],
                        "duration_seconds": ic_list[i + 1]["timestamp"] - ic_list[i]["timestamp"]
                    })
        
        # Sort cycles by start frame
        gait_cycles.sort(key=lambda x: x["start_frame"])
        
        return gait_cycles
    
    def _detect_gait_phases(self, gait_cycles: List[Dict[str, Any]], 
                          ic_events: List[Dict[str, Any]], 
                          to_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect gait phases within each cycle."""
        gait_phases = []
        
        for cycle in gait_cycles:
            foot = cycle["foot"]
            start_frame = cycle["start_frame"]
            end_frame = cycle["end_frame"]
            
            # Find TO event within this cycle
            cycle_to_events = [
                e for e in to_events 
                if e["foot"] == foot and start_frame < e["frame"] < end_frame
            ]
            
            if cycle_to_events:
                # Take the first TO event in the cycle
                to_frame = cycle_to_events[0]["frame"]
                
                # Stance phase: IC to TO
                stance_phase = {
                    "cycle_id": len(gait_phases),
                    "foot": foot,
                    "phase": "stance",
                    "start_frame": start_frame,
                    "end_frame": to_frame,
                    "duration_frames": to_frame - start_frame,
                }
                
                # Swing phase: TO to next IC
                swing_phase = {
                    "cycle_id": len(gait_phases),
                    "foot": foot,
                    "phase": "swing",
                    "start_frame": to_frame,
                    "end_frame": end_frame,
                    "duration_frames": end_frame - to_frame,
                }
                
                gait_phases.extend([stance_phase, swing_phase])
            else:
                # No TO event found, treat entire cycle as one phase
                full_phase = {
                    "cycle_id": len(gait_phases),
                    "foot": foot,
                    "phase": "complete",
                    "start_frame": start_frame,
                    "end_frame": end_frame,
                    "duration_frames": end_frame - start_frame,
                }
                gait_phases.append(full_phase)
        
        return gait_phases
    
    def _combine_events(self, ic_events: List[Dict[str, Any]], 
                       to_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Combine all gait events and sort by frame."""
        all_events = ic_events + to_events
        all_events.sort(key=lambda x: x["frame"])
        return all_events
