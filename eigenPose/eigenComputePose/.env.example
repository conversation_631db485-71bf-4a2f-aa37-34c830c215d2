# Database Configuration
# Use the same database URL as the frontend for consistency
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
DIRECT_URL=postgresql://username:password@localhost:5432/database_name

# API Configuration
BACKEND_API_HOST=0.0.0.0
BACKEND_API_PORT=8000
BACKEND_API_RELOAD=false
BACKEND_API_WORKERS=1

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.vercel.app

# Logging Configuration
LOG_LEVEL=INFO

# OpenPose Configuration
OPENPOSE_MODEL_FOLDER=/path/to/openpose/models
OPENPOSE_GPU_NUMBER=1

# Video Processing Configuration
MAX_VIDEO_SIZE_MB=100
MAX_PROCESSING_TIME_SECONDS=300

# Temporary file cleanup
CLEANUP_TEMP_FILES=true
TEMP_FILE_RETENTION_HOURS=24
