# Multi-stage Dockerfile for Gait Analysis Platform
FROM nvidia/cuda:11.5-cudnn8-devel-ubuntu20.04 as base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    cmake \
    build-essential \
    libopencv-dev \
    libprotobuf-dev \
    protobuf-compiler \
    libgoogle-glog-dev \
    libgflags-dev \
    libhdf5-dev \
    libatlas-base-dev \
    libboost-all-dev \
    wget \
    git \
    pkg-config \
    libcaffe-cuda-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy OpenPose (assuming it's built and available)
# Note: In production, you might want to build OpenPose in the container
COPY openpose /app/openpose

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p inputVideos outputResults temp logs

# Set Python path
ENV PYTHONPATH=/app:/app/openpose/build/python

# Expose API port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command (can be overridden)
CMD ["python3", "-m", "uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
