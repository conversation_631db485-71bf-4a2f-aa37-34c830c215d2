"""
FastAPI application for video gait analysis platform.
"""
import sys
import os
from pathlib import Path
import time
import json
import math
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import our modules
from config import get_config, INPUT_VIDEOS_DIR, OUTPUT_RESULTS_DIR
from modules import VideoProcessor, GaitCycleDetector, BiomechanicalAnalyzer
from modules.utils import validate_video_file, create_output_directory
from models import (
    AnalysisRequest, AnalysisResponse, BatchAnalysisRequest, BatchAnalysisResponse,
    HealthResponse, VideoListResponse, ConfigResponse, AnalysisResults
)

# Initialize FastAPI app
config = get_config()
api_config = config.get("api", {})

app = FastAPI(
    title=api_config.get("title", "Gait Analysis API"),
    description=api_config.get("description", "Video analysis platform for running gait analysis"),
    version=api_config.get("version", "1.0.0"),
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for processors (initialized on startup)
video_processor = None
gait_detector = None
biomech_analyzer = None

def sanitize_for_json(obj):
    """Recursively sanitize data structure for JSON serialization."""
    if isinstance(obj, dict):
        return {k: sanitize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, (int, str, bool)) or obj is None:
        return obj
    elif isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    elif isinstance(obj, np.ndarray):
        return sanitize_for_json(obj.tolist())
    elif hasattr(obj, '__dict__'):
        return sanitize_for_json(obj.__dict__)
    else:
        # For other types, try to convert to basic types
        try:
            if hasattr(obj, 'tolist'):
                return sanitize_for_json(obj.tolist())
            elif hasattr(obj, '__iter__') and not isinstance(obj, str):
                return [sanitize_for_json(item) for item in obj]
            else:
                return str(obj)
        except:
            return str(obj)

@app.on_event("startup")
async def startup_event():
    """Initialize processors on startup."""
    global video_processor, gait_detector, biomech_analyzer

    try:
        video_processor = VideoProcessor(config)
        gait_detector = GaitCycleDetector(config)
        biomech_analyzer = BiomechanicalAnalyzer(config)
        print("✅ Gait analysis processors initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize processors: {e}")
        # Don't fail startup, but processors will be None

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global video_processor
    if video_processor:
        video_processor.cleanup()

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint."""
    return {
        "message": "Video Gait Analysis Platform API",
        "version": api_config.get("version", "1.0.0"),
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check OpenPose availability
        openpose_available = video_processor is not None

        # Check GPU availability (basic check)
        gpu_available = False
        try:
            import subprocess
            result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
            gpu_available = result.returncode == 0
        except:
            pass

        return HealthResponse(
            status="healthy" if openpose_available else "degraded",
            timestamp=datetime.now(),
            version=api_config.get("version", "1.0.0"),
            openpose_available=openpose_available,
            gpu_available=gpu_available
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.get("/config", response_model=ConfigResponse)
async def get_configuration():
    """Get current configuration."""
    return ConfigResponse(config=config)

@app.get("/videos", response_model=VideoListResponse)
async def list_videos():
    """List available videos in input directory."""
    try:
        supported_formats = config["video"]["supported_formats"]
        video_files = []

        for format_ext in supported_formats:
            video_files.extend(INPUT_VIDEOS_DIR.glob(f"*{format_ext}"))
            video_files.extend(INPUT_VIDEOS_DIR.glob(f"*{format_ext.upper()}"))

        videos = []
        for video_path in video_files:
            try:
                stat = video_path.stat()
                videos.append({
                    "filename": video_path.name,
                    "size_bytes": stat.st_size,
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "path": str(video_path.relative_to(INPUT_VIDEOS_DIR))
                })
            except Exception as e:
                print(f"Error getting info for {video_path}: {e}")

        return VideoListResponse(
            videos=sorted(videos, key=lambda x: x["filename"]),
            total_count=len(videos)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list videos: {str(e)}")

@app.post("/upload")
async def upload_video(file: UploadFile = File(...)):
    """Upload a video file for analysis."""
    try:
        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in config["video"]["supported_formats"]:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file format: {file_ext}"
            )

        # Save uploaded file
        upload_path = INPUT_VIDEOS_DIR / file.filename

        with open(upload_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        return {
            "message": "Video uploaded successfully",
            "filename": file.filename,
            "size_bytes": len(content),
            "path": str(upload_path)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

def process_video_analysis(video_filename: str, visualize: bool = False, config_overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Process video analysis (used by both sync and async endpoints)."""
    start_time = time.time()

    try:
        # Check if processors are available
        if not all([video_processor, gait_detector, biomech_analyzer]):
            raise ValueError("Analysis processors not initialized")

        # Find video file
        video_path = INPUT_VIDEOS_DIR / video_filename
        if not video_path.exists():
            raise ValueError(f"Video file not found: {video_filename}")

        # Validate video
        if not validate_video_file(video_path, config["video"]["supported_formats"]):
            raise ValueError(f"Invalid video file: {video_filename}")

        # Apply config overrides if provided
        analysis_config = config.copy()
        if config_overrides:
            analysis_config.update(config_overrides)

        # Create output directory
        video_name = video_path.stem
        output_dir = create_output_directory(OUTPUT_RESULTS_DIR, video_name)

        # Process video with OpenPose
        pose_results = video_processor.process_video(video_path, output_dir, visualize)

        # Extract keypoints
        keypoints_sequence = video_processor.get_primary_person_keypoints(
            pose_results["keypoints_data"]
        )

        if not keypoints_sequence:
            raise ValueError("No valid keypoints detected in video")

        fps = pose_results["video_properties"]["fps"]

        # Detect gait cycles
        gait_data = gait_detector.detect_gait_cycles(keypoints_sequence, fps)

        # Calculate measurements
        measurements = biomech_analyzer.analyze_gait_measurements(
            keypoints_sequence, gait_data, fps
        )

        # Generate visualizations if requested
        visualization_files = []
        if visualize:
            from modules.visualizer import GaitVisualizer
            visualizer = GaitVisualizer(analysis_config)
            visualization_files = visualizer.create_visualizations(
                video_path, output_dir, pose_results, gait_data, keypoints_sequence, fps
            )

        # Compile results
        results = {
            "video_info": {
                "path": str(video_path),
                "name": video_name,
                "properties": pose_results["video_properties"],
            },
            "processing_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "processing_time_seconds": time.time() - start_time,
                "total_frames": len(keypoints_sequence),
                "frames_with_detection": pose_results["frames_with_detection"],
                "detection_rate": pose_results["frames_with_detection"] / len(keypoints_sequence),
            },
            "gait_analysis": {
                "cycles": gait_data["cycles"],
                "phases": gait_data["phases"],
                "events": gait_data["events"],
                "total_cycles": gait_data["total_cycles"],
            },
            "biomechanical_measurements": measurements,
            "output_directory": str(output_dir),
            "visualization_files": visualization_files if visualize else [],
        }

        # Save results
        results_file = output_dir / "gait_analysis_results.json"
        with open(results_file, 'w') as f:
            json.dump(sanitize_for_json(results), f, indent=2)

        # Sanitize results for JSON serialization
        sanitized_results = sanitize_for_json(results)

        return {
            "success": True,
            "message": "Analysis completed successfully",
            "results": sanitized_results,
            "processing_time": time.time() - start_time
        }

    except Exception as e:
        return {
            "success": False,
            "message": "Analysis failed",
            "error": str(e),
            "processing_time": time.time() - start_time
        }

@app.post("/analyze")
async def analyze_video(request: AnalysisRequest):
    """Analyze a single video file."""
    result = process_video_analysis(request.video_filename, request.visualize_results, request.config_overrides)

    if result["success"]:
        return JSONResponse(content=result)
    else:
        raise HTTPException(
            status_code=500,
            detail=result["error"]
        )

@app.post("/analyze/batch", response_model=BatchAnalysisResponse)
async def analyze_batch(request: BatchAnalysisRequest):
    """Analyze all videos in the input directory."""
    start_time = time.time()

    try:
        # Find all video files
        supported_formats = config["video"]["supported_formats"]
        video_files = []

        for format_ext in supported_formats:
            video_files.extend(INPUT_VIDEOS_DIR.glob(f"*{format_ext}"))
            video_files.extend(INPUT_VIDEOS_DIR.glob(f"*{format_ext.upper()}"))

        if not video_files:
            raise HTTPException(status_code=404, detail="No video files found")

        # Process each video
        results = []
        successful = 0
        failed = 0

        for video_path in video_files:
            result = process_video_analysis(video_path.name, request.config_overrides)
            results.append(result)

            if result["success"]:
                successful += 1
            else:
                failed += 1

        return BatchAnalysisResponse(
            success=True,
            message=f"Batch analysis completed: {successful} successful, {failed} failed",
            total_videos=len(video_files),
            successful_analyses=successful,
            failed_analyses=failed,
            results=results,
            processing_time=time.time() - start_time
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")

@app.get("/results/{video_name}")
async def get_results(video_name: str):
    """Get analysis results for a specific video."""
    try:
        results_dir = OUTPUT_RESULTS_DIR / f"{video_name}_analysis"
        results_file = results_dir / "gait_analysis_results.json"

        if not results_file.exists():
            raise HTTPException(status_code=404, detail="Results not found")

        with open(results_file) as f:
            results = json.load(f)

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get results: {str(e)}")

@app.get("/results/{video_name}/download")
async def download_results(video_name: str):
    """Download analysis results as JSON file."""
    try:
        results_dir = OUTPUT_RESULTS_DIR / f"{video_name}_analysis"
        results_file = results_dir / "gait_analysis_results.json"

        if not results_file.exists():
            raise HTTPException(status_code=404, detail="Results not found")

        return FileResponse(
            path=results_file,
            filename=f"{video_name}_gait_analysis.json",
            media_type="application/json"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download results: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host=api_config.get("host", "0.0.0.0"),
        port=api_config.get("port", 8000),
        reload=api_config.get("reload", False),
        workers=api_config.get("workers", 1)
    )
