"""
Pydantic models for API requests and responses.
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class VideoInfo(BaseModel):
    """Video information model."""
    path: str
    name: str
    properties: Dict[str, Any]

class ProcessingInfo(BaseModel):
    """Processing information model."""
    timestamp: str
    processing_time_seconds: float
    total_frames: int
    frames_with_detection: int
    detection_rate: float

class GaitCycle(BaseModel):
    """Gait cycle model."""
    foot: str
    start_frame: int
    end_frame: int
    duration_frames: int
    start_timestamp: float
    end_timestamp: float
    duration_seconds: float

class GaitEvent(BaseModel):
    """Gait event model."""
    frame: int
    timestamp: float
    foot: str
    event_type: str
    method: str
    confidence: float
    position: List[float]

class GaitPhase(BaseModel):
    """Gait phase model."""
    cycle_id: int
    foot: str
    phase: str
    start_frame: int
    end_frame: int
    duration_frames: int

class GaitAnalysis(BaseModel):
    """Gait analysis results model."""
    cycles: List[GaitCycle]
    phases: List[GaitPhase]
    events: List[GaitEvent]
    total_cycles: int

class BiomechanicalMeasurements(BaseModel):
    """Biomechanical measurements model."""
    pelvicDrop: float = Field(description="Pelvic drop direction analysis")
    Pronation: float = Field(description="Ankle pronation/supination angle")
    kneeDrift: float = Field(description="Knee valgus/varus at midstance")
    footDrift: float = Field(description="Foot crossover gait detection")
    heelWhip: float = Field(description="Medial/lateral heel movement")
    posteriorStabilityScore: float = Field(description="Composite stability score")
    overstride: float = Field(description="Heel-to-hip distance at initial contact")
    ankleDorsiflexion: float = Field(description="Foot strike type classification")
    anklePlantarflexion: float = Field(description="Propulsive power and push-off")
    verticalOscillationVideo: float = Field(description="Vertical oscillation in cm")
    trunkLean: float = Field(description="Trunk lean angle in degrees")
    Ground_contact_Time: float = Field(description="IC to TO duration in ms")
    Cadence: float = Field(description="Steps per second")
    kneeFlexionLoading: float = Field(description="Knee flexion during loading phase")
    body_height_pixels: float = Field(description="Estimated body height in pixels")
    total_frames: int = Field(description="Total frames analyzed")
    fps: float = Field(description="Video frame rate")
    analysis_method: str = Field(description="Analysis method used")

class AnalysisResults(BaseModel):
    """Complete analysis results model."""
    video_info: VideoInfo
    processing_info: ProcessingInfo
    gait_analysis: GaitAnalysis
    biomechanical_measurements: BiomechanicalMeasurements
    output_directory: str

class AnalysisRequest(BaseModel):
    """Analysis request model."""
    video_filename: str = Field(description="Name of video file in input directory")
    visualize_results: bool = Field(
        default=False,
        description="Whether to generate visualization outputs (video and images)"
    )
    config_overrides: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional configuration overrides"
    )

class AnalysisResponse(BaseModel):
    """Analysis response model."""
    success: bool
    message: str
    results: Optional[AnalysisResults] = None
    error: Optional[str] = None
    processing_time: float

class BatchAnalysisRequest(BaseModel):
    """Batch analysis request model."""
    config_overrides: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional configuration overrides"
    )

class BatchAnalysisResponse(BaseModel):
    """Batch analysis response model."""
    success: bool
    message: str
    total_videos: int
    successful_analyses: int
    failed_analyses: int
    results: List[Dict[str, Any]]
    processing_time: float

class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    openpose_available: bool
    gpu_available: bool

class VideoListResponse(BaseModel):
    """Video list response model."""
    videos: List[Dict[str, Any]]
    total_count: int

class ConfigResponse(BaseModel):
    """Configuration response model."""
    config: Dict[str, Any]
