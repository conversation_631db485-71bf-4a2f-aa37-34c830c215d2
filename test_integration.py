#!/usr/bin/env python3
"""
Test script for video analysis backend integration.
This script tests the backend API endpoints and database connectivity.
"""

import requests
import json
import os
import sys
from pathlib import Path

# Configuration
BACKEND_URL = os.getenv('BACKEND_API_URL', 'http://localhost:8000')
TEST_VIDEO_URL = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"  # Sample video for testing

def test_backend_health():
    """Test backend health endpoint."""
    print("🔍 Testing backend health...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend is healthy")
            print(f"   Status: {data.get('status')}")
            print(f"   Version: {data.get('version')}")
            print(f"   OpenPose Available: {data.get('openpose_available')}")
            print(f"   GPU Available: {data.get('gpu_available')}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_backend_config():
    """Test backend configuration endpoint."""
    print("\n🔧 Testing backend configuration...")
    try:
        response = requests.get(f"{BACKEND_URL}/config", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend configuration retrieved")
            print(f"   API Host: {data.get('config', {}).get('api', {}).get('host')}")
            print(f"   API Port: {data.get('config', {}).get('api', {}).get('port')}")
            return True
        else:
            print(f"❌ Backend config failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot get backend config: {e}")
        return False

def test_video_list():
    """Test video listing endpoint."""
    print("\n📹 Testing video list endpoint...")
    try:
        response = requests.get(f"{BACKEND_URL}/videos", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Video list retrieved")
            print(f"   Total videos: {data.get('total_count', 0)}")
            return True
        else:
            print(f"❌ Video list failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot get video list: {e}")
        return False

def test_video_analysis():
    """Test video analysis endpoint (without actual processing)."""
    print("\n🧠 Testing video analysis endpoint...")
    
    # Test data
    test_data = {
        "video_url": TEST_VIDEO_URL,
        "profile_id": "test-profile-123",
        "video_type": "sagittal",
        "user_id": "test-user-123",
        "visualize_results": False  # Skip visualization for faster testing
    }
    
    try:
        print(f"   Sending test request to {BACKEND_URL}/analyze-url")
        print(f"   Test video URL: {TEST_VIDEO_URL}")
        
        # Note: This will likely fail if OpenPose is not properly configured
        # but it tests the API endpoint structure
        response = requests.post(
            f"{BACKEND_URL}/analyze-url",
            json=test_data,
            timeout=60  # Longer timeout for video processing
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Video analysis endpoint responded successfully")
            print(f"   Success: {data.get('success')}")
            if data.get('success'):
                print(f"   Processing time: {data.get('processing_time', 0):.2f}s")
            else:
                print(f"   Error: {data.get('error')}")
            return True
        else:
            print(f"⚠️  Video analysis failed (expected if OpenPose not configured)")
            print(f"   Status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Video analysis request failed: {e}")
        return False

def test_database_connection():
    """Test database connectivity (indirectly through backend)."""
    print("\n🗄️  Testing database connectivity...")
    
    # We can't directly test the database, but we can check if the backend
    # can handle database operations by looking at the health endpoint
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Backend can respond (database connection likely working)")
            return True
        else:
            print("⚠️  Backend response issues (database might be down)")
            return False
    except Exception as e:
        print(f"❌ Cannot test database connectivity: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Video Analysis Backend Integration Tests")
    print(f"   Backend URL: {BACKEND_URL}")
    print("=" * 60)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Backend Config", test_backend_config),
        ("Video List", test_video_list),
        ("Database Connection", test_database_connection),
        ("Video Analysis", test_video_analysis),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Integration is working correctly.")
        return 0
    elif passed >= total // 2:
        print("⚠️  Some tests failed, but basic functionality is working.")
        print("   Check the failed tests and ensure proper configuration.")
        return 1
    else:
        print("❌ Most tests failed. Check backend configuration and connectivity.")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
